package fr.enedis.i2r.si.mock;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import io.javalin.http.Context;
import io.javalin.http.HttpStatus;

class SiControllerTest {

    @Mock
    private Context context;

    private SiController siController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        siController = new SiController();
    }

    @Test
    void une_configuration_avec_idms_valide_et_contenu_valide_retourne_ok() {
        String validIdms = "12345678901234567890123456789012"; // 32 characters
        String validConfig = """
            {
                "class": "Container",
                "name": "config",
                "objects": []
            }
            """;

        when(context.pathParam("idms")).thenReturn(validIdms);
        when(context.header("Content-Encoding")).thenReturn(null);
        when(context.body()).thenReturn(validConfig);
        when(context.status(any(HttpStatus.class))).thenReturn(context);

        siController.receiveConfiguration(context);

        verify(context).status(HttpStatus.OK);
        verify(context).result(HttpStatus.OK.toString());
    }

    @Test
    void un_idms_de_longueur_invalide_retourne_bad_request() {
        String invalidIdms = "12345"; // Less than 32 characters

        when(context.pathParam("idms")).thenReturn(invalidIdms);
        when(context.status(anyInt())).thenReturn(context);

        siController.receiveConfiguration(context);

        verify(context).status(HttpStatus.BAD_REQUEST.getCode());
        verify(context).result("IDMS is invalid");
    }

    @Test
    void un_idms_null_retourne_bad_request() {
        when(context.pathParam("idms")).thenReturn(null);
        when(context.status(anyInt())).thenReturn(context);

        siController.receiveConfiguration(context);

        verify(context).status(HttpStatus.BAD_REQUEST.getCode());
        verify(context).result("IDMS is invalid");
    }

    @Test
    void une_configuration_avec_encodage_gzip_invalide_retourne_bad_request() throws IOException {
        String validIdms = "12345678901234567890123456789012";
        String validConfig = """
            {
                "class": "Container",
                "name": "config",
                "objects": []
            }
            """;

        InputStream compressedStream = new ByteArrayInputStream(validConfig.getBytes());

        when(context.pathParam("idms")).thenReturn(validIdms);
        when(context.header("Content-Encoding")).thenReturn("gzip");
        when(context.bodyInputStream()).thenReturn(compressedStream);
        when(context.status(anyInt())).thenReturn(context);

        siController.receiveConfiguration(context);

        verify(context).status(HttpStatus.BAD_REQUEST.getCode());
        verify(context).result("Invalid body data.");
    }

    @Test
    void une_configuration_avec_json_invalide_leve_une_exception() {
        String validIdms = "12345678901234567890123456789012";
        String invalidConfig = "{ invalid json }";

        when(context.pathParam("idms")).thenReturn(validIdms);
        when(context.header("Content-Encoding")).thenReturn(null);
        when(context.body()).thenReturn(invalidConfig);
        when(context.status(any(HttpStatus.class))).thenReturn(context);

        // The validator throws an exception for invalid JSON, which is caught and handled
        assertThrows(IllegalArgumentException.class, () -> {
            siController.receiveConfiguration(context);
        });
    }

    @Test
    void une_configuration_vide_retourne_ok() {
        String validIdms = "12345678901234567890123456789012";
        String emptyConfig = "";

        when(context.pathParam("idms")).thenReturn(validIdms);
        when(context.header("Content-Encoding")).thenReturn(null);
        when(context.body()).thenReturn(emptyConfig);
        when(context.status(any(HttpStatus.class))).thenReturn(context);

        siController.receiveConfiguration(context);

        // Empty configuration is actually considered valid by the current validator
        verify(context).status(HttpStatus.OK);
        verify(context).result(HttpStatus.OK.toString());
    }

    @Test
    void des_metriques_valides_avec_idms_valide_retournent_ok() {
        String validIdms = "12345678901234567890123456789012";
        String validMetrics = """
            {
                "class": "Container",
                "cmdKey": null,
                "emitted": "2025-09-22T13:37:41Z",
                "entity": "LOGDM",
                "flow": "/db/cfg/flows/stat",
                "modified": "2025-09-21T22:00:02Z",
                "name": "test",
                "nb": 0,
                "objects": [],
                "pub": true
            }
            """;

        when(context.pathParam("idms")).thenReturn(validIdms);
        when(context.header("Content-Encoding")).thenReturn(null);
        when(context.body()).thenReturn(validMetrics);
        when(context.status(any(HttpStatus.class))).thenReturn(context);

        siController.receiveMetrics(context);

        verify(context).status(HttpStatus.OK);
        verify(context).result(HttpStatus.OK.toString());
    }

    @Test
    void des_metriques_avec_encodage_gzip_invalide_retournent_bad_request() throws IOException {
        String validIdms = "12345678901234567890123456789012";
        String invalidMetrics = """
            {
                "class": "Container",
                "emitted": "2025-09-22T13:37:41Z",
                "entity": "LOGDM",
                "name": "test",
                "nb": 0,
                "objects": [],
                "pub": true
            }
            """;

        InputStream invalidGzipStream = new ByteArrayInputStream(invalidMetrics.getBytes());

        when(context.pathParam("idms")).thenReturn(validIdms);
        when(context.header("Content-Encoding")).thenReturn("gzip");
        when(context.bodyInputStream()).thenReturn(invalidGzipStream);
        when(context.status(anyInt())).thenReturn(context);

        siController.receiveMetrics(context);

        verify(context).status(HttpStatus.BAD_REQUEST.getCode());
        verify(context).result("Le corps de requête est invalide.");
    }

    @Test
    void des_metriques_avec_json_invalide_retournent_bad_request() {
        String validIdms = "12345678901234567890123456789012";
        String invalidMetrics = """
            {
                "class": "Container",
                "entity": "LOGDM",
                "name": "test",
                "pub": false
            }
            """;

        when(context.pathParam("idms")).thenReturn(validIdms);
        when(context.header("Content-Encoding")).thenReturn(null);
        when(context.body()).thenReturn(invalidMetrics);
        when(context.status(any(HttpStatus.class))).thenReturn(context);

        siController.receiveMetrics(context);

        verify(context).status(HttpStatus.BAD_REQUEST);
        verify(context).result(org.mockito.ArgumentMatchers.contains("Métriques du boitier invalides"));
    }


}
