package fr.enedis.i2r.si.mock;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import io.javalin.Javalin;

public class E2ETestEndpoints {

    private static final Logger logger = LoggerFactory.getLogger(E2ETestEndpoints.class);

    // Simple state storage for E2E tests
    private static volatile int currentBipState = 1;

    public static void setupMockRoutes(Javalin app) {
        app.get("/api/i2r/time/sync", ctx -> {
            ctx.status(200);
            ctx.result("true");
        });

        app.post("/api/i2r/log/app", ctx -> {
            String logLevel = ctx.queryParam("logLevel");
            if (logLevel == null || logLevel.trim().isEmpty()) {
                ctx.status(400);
                ctx.result("niveau de log manquant");
            } else {
                ctx.status(200);
                ctx.result("Le niveau de log i2R a bien été modifié à : " + logLevel);
            }
        });

        app.get("/db/cfg", ctx -> {
            String depth = ctx.queryParam("depth");
            String configJson = """
                {
                    "configurationHash": "abc123def456",
                    "version": "1.0.1",
                    "status": "STABLE",
                    "lastUpdate": "2025-10-23T13:00:00Z",
                    "depth": "%s",
                    "idms": "12345678901234567890123456789012"
                }
                """.formatted(depth != null ? depth : "default");
            ctx.status(200);
            ctx.contentType("application/json");
            ctx.result(configJson);
        });

        app.put("/db/cfg/dm", ctx -> {
            try {
                String body = ctx.body();
                if (body == null || body.trim().isEmpty() || body.equals("{}")) {
                    ctx.status(400);
                    ctx.result("État invalide");
                    return;
                }

                if (!body.contains("\"state\"")) {
                    ctx.status(400);
                    ctx.result("État invalide");
                    return;
                }

                int state;
                try {
                    String stateStr = body.replaceAll(".*\"state\"\\s*:\\s*(\\d+).*", "$1");
                    state = Integer.parseInt(stateStr);
                    if (state != 1 && state != 2) {
                        ctx.status(400);
                        ctx.result("État invalide");
                        return;
                    }
                } catch (NumberFormatException e) {
                    ctx.status(400);
                    ctx.result("État invalide");
                    return;
                }

                String apiVersion = ctx.header("X-ERDF-API-VERSION");
                String idms = ctx.header("x-idms");
                String hash = ctx.header("X-ERDF-HASH");

                if (apiVersion == null || idms == null || hash == null) {
                    ctx.status(400);
                    ctx.result("En-têtes manquants");
                    return;
                }
                if (!"12345678901234567890123456789012".equals(idms)) {
                    ctx.status(400);
                    ctx.result("IDMS incorrect");
                    return;
                }

                if (!"abc123def456".equals(hash)) {
                    ctx.status(400);
                    ctx.result("Hash incorrect");
                    return;
                }

                currentBipState = state;
                logger.info("Device state updated to: {} ({})", state, state == 1 ? "INIT" : "STABLE");

                ctx.status(200);
                ctx.result("État du boîtier modifié avec succès");

            } catch (Exception e) {
                ctx.status(400);
                ctx.result("Erreur lors du traitement de la requête");
            }
        });

        app.get("/trigger-cfg-request", ctx -> {
            String targetState = ctx.queryParam("targetState");
            String idms = ctx.queryParam("idms");

            if (targetState == null || idms == null) {
                ctx.status(400);
                ctx.result("Paramètres manquants: targetState et idms requis");
                return;
            }

            logger.info("Trigger configuration request: targetState={}, idms={}", targetState, idms);

            // Simulate state change by calling the BIP state endpoint
            try {
                int stateNumber = "STABLE".equals(targetState) ? 2 : 1;
                String stateChangeBody = "{\"state\": " + stateNumber + "}";

                Map<String, String> headers = new HashMap<>();
                headers.put("X-ERDF-API-VERSION", "1.0");
                headers.put("x-idms", idms);
                headers.put("X-ERDF-HASH", "abc123def456");

                logger.info("Triggering state change to {} (state={})", targetState, stateNumber);

                java.net.http.HttpClient client = java.net.http.HttpClient.newHttpClient();
                java.net.http.HttpRequest.Builder requestBuilder = java.net.http.HttpRequest.newBuilder()
                    .uri(java.net.URI.create("http://localhost:8440/db/cfg/dm"))
                    .PUT(java.net.http.HttpRequest.BodyPublishers.ofString(stateChangeBody))
                    .header("Content-Type", "application/json");

                for (Map.Entry<String, String> header : headers.entrySet()) {
                    requestBuilder.header(header.getKey(), header.getValue());
                }

                java.net.http.HttpRequest request = requestBuilder.build();
                java.net.http.HttpResponse<String> response = client.send(request,
                    java.net.http.HttpResponse.BodyHandlers.ofString());

                logger.info("State change response: status={}, body={}", response.statusCode(), response.body());

                if (response.statusCode() == 200) {
                    ctx.status(200);
                    ctx.result("Configuration request triggered successfully - state changed to " + targetState);
                } else {
                    ctx.status(500);
                    ctx.result("Failed to change state: " + response.body());
                }
            } catch (Exception e) {
                logger.error("Error triggering state change", e);
                ctx.status(500);
                ctx.result("Error triggering state change: " + e.getMessage());
            }
        });

        app.get("/bip/state", ctx -> {
            String stateName = currentBipState == 1 ? "INIT" : "STABLE";
            String response = String.format("{\"state\": %d, \"stateName\": \"%s\"}", currentBipState, stateName);
            ctx.status(200);
            ctx.contentType("application/json");
            ctx.result(response);
        });
    }
}
