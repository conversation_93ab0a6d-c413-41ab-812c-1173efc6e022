<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Enable debug logging for our test classes -->
    <logger name="fr.enedis.i2r.e2e" level="DEBUG"/>
    
    <!-- Keep other loggers at INFO level -->
    <root level="INFO">
        <appender-ref ref="STDOUT" />
    </root>
</configuration>
