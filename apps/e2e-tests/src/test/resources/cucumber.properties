# Configuration Cucumber pour les tests E2E I2R

# Packages contenant les step definitions
cucumber.glue=fr.enedis.i2r.e2e.steps

# Répertoire contenant les fichiers .feature
cucumber.features=classpath:features

# Plugins pour les rapports
cucumber.plugin=pretty,html:target/cucumber-reports,json:target/cucumber-reports/Cucumber.json,junit:target/cucumber-reports/Cucumber.xml

# Options d'affichage
cucumber.publish.quiet=true
cucumber.publish.enabled=false

# Stratégie de nommage pour JUnit Platform
cucumber.junit-platform.naming-strategy=long

# Filtres pour exécuter des tests spécifiques (optionnel)
# cucumber.filter.tags=@smoke or @regression

# Mode strict - échoue si des step definitions sont manquantes
cucumber.execution.strict=true

# Ordre d'exécution des scénarios
cucumber.execution.order=lexical

# Parallélisation (désactivée par défaut pour les tests E2E)
cucumber.execution.parallel.enabled=false

# Snippets en français
cucumber.snippet-type=camelcase
