# Configuration JUnit Platform pour les tests E2E I2R

# Configuration Cucumber
cucumber.execution.parallel.enabled=false
cucumber.execution.parallel.mode.default=concurrent
cucumber.execution.parallel.mode.features=concurrent
cucumber.execution.parallel.mode.scenarios=concurrent

# Timeout par défaut pour les tests (en secondes)
junit.jupiter.execution.timeout.default=300

# Mode de découverte des tests
junit.jupiter.testinstance.lifecycle.default=per_class

# Configuration des logs
java.util.logging.manager=org.apache.logging.log4j.jul.LogManager

# Désactiver les bannières JUnit
junit.platform.output.capture.stdout=true
junit.platform.output.capture.stderr=true

# Configuration des rapports
junit.platform.reporting.open.xml.enabled=true
junit.platform.reporting.output.dir=target/test-reports
