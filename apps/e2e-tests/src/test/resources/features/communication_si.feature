# language: fr
Fonctionnalité: Communication avec le Système d'Information

  En tant qu'application I2R
  Je veux communiquer avec le Système d'Information
  Afin d'envoyer ma configuration et mes métriques

  Contexte:
    Étant donné que l'application I2R est démarrée
    Et que le SI est accessible
    Et que le boîtier est à l'état "INIT"
    Et que l'IDMS du boîtier est "12345678901234567890123456789012"
    Et que le hash de configuration est "abc123def456"

  Scénario: Envoi de configuration au démarrage en état INIT
    Quand l'application envoie la configuration au SI
    Alors la configuration devrait être envoyée au SI
    Et la communication avec le SI devrait être établie

  Scénario: Envoi de métriques au SI
    Étant donné que le boîtier est à l'état "STABLE"
    Quand l'application envoie des métriques au SI
    Alors les métriques devraient être envoyées au SI

  Scénario: Changement d'état demandé par le SI
    Quand le SI demande un changement d'état vers "STABLE"
    Alors le changement d'état devrait réussir
    Et l'état du boîtier devrait être "STABLE"

  Scénario: Réception de configuration depuis le SI
    Quand le SI envoie une configuration au boîtier
    Alors le code de statut de la réponse devrait être 200
    Et le SI devrait recevoir la requête avec l'IDMS "12345678901234567890123456789012"
    Et le SI devrait recevoir la requête avec le hash "abc123def456"

  Scénario: Gestion de la panne du SI
    Quand je simule une panne du SI
    Alors la communication avec le SI devrait échouer
    Et l'application devrait basculer sur le datacenter secondaire

  Scénario: Restauration après panne du SI
    Étant donné que je simule une panne du SI
    Quand je restaure le SI
    Et j'attends 5 secondes
    Alors la communication avec le SI devrait être établie
    Et l'application devrait réessayer l'envoi

  Scénario: Redondance des datacenters
    Étant donné que le datacenter principal est indisponible
    Quand l'application envoie la configuration au SI
    Alors l'application devrait basculer sur le datacenter secondaire
    Et la configuration devrait être envoyée au SI

  Plan du scénario: Envoi de configuration selon l'état du boîtier
    Étant donné que le boîtier est à l'état "<etat_boitier>"
    Quand l'application envoie la configuration au SI
    Alors la configuration devrait être envoyée au SI

    Exemples:
      | etat_boitier |
      | INIT         |
      | STABLE       |
