# language: fr
Fonctionnalité: API REST de l'application I2R

  En tant qu'utilisateur de l'API
  Je veux pouvoir accéder aux différents endpoints REST
  Afin de surveiller et configurer l'application

  Contexte:
    Étant donné que l'application I2R est démarrée

  Scénario: Vérification de la synchronisation temporelle
    Quand j'effectue une requête GET sur "/api/i2r/time/sync"
    Alors le code de statut de la réponse devrait être 200
    Et la réponse devrait contenir "true"

  Scénario: Changement du niveau de log
    Quand j'effectue une requête POST sur "/api/i2r/log/app" avec les paramètres:
      | logLevel | ERROR |
    Alors le code de statut de la réponse devrait être 200
    Et la réponse devrait contenir "Le niveau de log i2R a bien été modifié à : ERROR"

  Scénario: Changement du niveau de log avec paramètre manquant
    Quand j'effectue une requête POST sur "/api/i2r/log/app"
    Alors le code de statut de la réponse devrait être 400
    Et la réponse devrait contenir "niveau de log manquant"

  Scénario: Récupération de la configuration complète
    Quand j'effectue une requête GET sur "/db/cfg"
    Alors le code de statut de la réponse devrait être 200
    Et la réponse devrait être un JSON valide
    Et la réponse JSON devrait contenir le champ "configurationHash"

  Scénario: Récupération de la configuration avec profondeur
    Quand j'effectue une requête GET sur "/db/cfg?depth=10"
    Alors le code de statut de la réponse devrait être 200
    Et la réponse devrait être un JSON valide

  Scénario: Changement d'état du boîtier via API
    Quand j'effectue une requête PUT sur "/db/cfg/dm" avec les en-têtes:
      | X-ERDF-API-VERSION | 1.0           |
      | x-idms             | 12345678901234567890123456789012 |
      | X-ERDF-HASH        | abc123def456  |
    Et j'effectue une requête PUT sur "/db/cfg/dm" avec le corps:
      """
      {"state": 2}
      """
    Alors le code de statut de la réponse devrait être 200

  Scénario: Changement d'état avec en-têtes manquants
    Quand j'effectue une requête PUT sur "/db/cfg/dm" avec le corps:
      """
      {"state": 2}
      """
    Alors le code de statut de la réponse devrait être 400

  Scénario: Changement d'état avec état invalide
    Quand j'effectue une requête PUT sur "/db/cfg/dm" avec les en-têtes:
      | X-ERDF-API-VERSION | 1.0           |
      | x-idms             | 12345678901234567890123456789012 |
      | X-ERDF-HASH        | abc123def456  |
    Et j'effectue une requête PUT sur "/db/cfg/dm" avec le corps:
      """
      {"state": 999}
      """
    Alors le code de statut de la réponse devrait être 400

  Plan du scénario: Test des niveaux de log
    Quand j'effectue une requête POST sur "/api/i2r/log/app" avec les paramètres:
      | logLevel | <niveau> |
    Alors le code de statut de la réponse devrait être 200
    Et la réponse devrait contenir "Le niveau de log i2R a bien été modifié à : <niveau>"

    Exemples:
      | niveau |
      | DEBUG  |
      | INFO   |
      | WARN   |
      | ERROR  |

  Plan du scénario: Test des états du boîtier
    Quand j'effectue une requête PUT sur "/db/cfg/dm" avec les en-têtes:
      | X-ERDF-API-VERSION | 1.0           |
      | x-idms             | 12345678901234567890123456789012 |
      | X-ERDF-HASH        | abc123def456  |
    Et j'effectue une requête PUT sur "/db/cfg/dm" avec le corps:
      """
      {"state": <code_etat>}
      """
    Alors le code de statut de la réponse devrait être 200

    Exemples:
      | code_etat |
      | 1         |
      | 2         |
