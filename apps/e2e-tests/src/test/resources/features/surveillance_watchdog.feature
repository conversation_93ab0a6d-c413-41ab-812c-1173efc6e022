# language: fr
Fonctionnalité: Surveillance et Watchdog

  En tant que système de surveillance
  Je veux pouvoir surveiller l'état de l'application I2R
  Afin de détecter les pannes et redémarrages automatiques

  Contexte:
    Étant donné que l'application I2R est démarrée

  Scénario: Vérification du fonctionnement du watchdog
    Quand j'attends 10 secondes
    Alors l'application I2R devrait être en cours d'exécution
    Et l'API REST devrait être accessible

  Scénario: Surveillance des threads critiques
    Étant donné que l'application I2R est démarrée
    Quand j'attends 5 secondes
    Alors l'application I2R devrait être en cours d'exécution

  Scénario: Vérification de la résilience de l'application
    Étant donné que l'application I2R est démarrée
    Quand j'attends 30 secondes
    Alors l'application I2R devrait être en cours d'exécution
    Et l'API REST devrait être accessible

  Scénario: Test de charge légère sur l'API
    Quand j'effectue une requête GET sur "/api/i2r/time/sync"
    Et j'attends 1 secondes
    Et j'effectue une requête GET sur "/api/i2r/time/sync"
    Et j'attends 1 secondes
    Et j'effectue une requête GET sur "/api/i2r/time/sync"
    Alors le code de statut de la réponse devrait être 200
    Et l'application I2R devrait être en cours d'exécution

  Scénario: Vérification de la stabilité après changements d'état
    Étant donné que le boîtier est à l'état "INIT"
    Et que l'IDMS du boîtier est "TEST-IDMS-123"
    Et que le hash de configuration est "abc123def456"
    Quand je change l'état du boîtier vers "STABLE"
    Et j'attends 5 secondes
    Et je change l'état du boîtier vers "INIT"
    Et j'attends 5 secondes
    Alors l'application I2R devrait être en cours d'exécution
    Et l'API REST devrait être accessible

  Scénario: Surveillance de la mémoire et des ressources
    Quand j'effectue une requête GET sur "/api/i2r/time/sync"
    Et j'attends 2 secondes
    Et j'effectue une requête GET sur "/db/cfg"
    Et j'attends 2 secondes
    Et j'effectue une requête GET sur "/api/i2r/time/sync"
    Alors l'application I2R devrait être en cours d'exécution
    Et le code de statut de la réponse devrait être 200
