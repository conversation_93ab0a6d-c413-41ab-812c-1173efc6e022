# language: fr
Fonctionnalité: Gestion de l'application I2R

  En tant qu'administrateur système
  Je veux pouvoir démarrer et arrêter l'application I2R
  Afin de gérer le cycle de vie de l'application

  Contexte:
    Étant donné que l'application I2R est arrêtée

  Scénario: Démarrage réussi de l'application I2R
    Quand je démarre l'application I2R
    Alors l'application I2R devrait être en cours d'exécution
    Et l'API REST devrait être accessible
    Et le SI mock devrait être accessible

  Scénario: Arrêt de l'application I2R
    Étant donné que l'application I2R est démarrée
    Quand j'arrête l'application I2R
    Alors l'application I2R devrait être arrêtée

  Scénario: Vérification de la disponibilité des services
    Étant donné que l'application I2R est démarrée
    Quand j'effectue une requête GET sur "/api/i2r/time/sync"
    Alors le code de statut de la réponse devrait être 200
    Et la réponse devrait contenir "true"

  Scénario: Accès à la documentation Swagger
    Étant donné que l'application I2R est démarrée
    Quand j'effectue une requête GET sur "/swagger"
    Alors le code de statut de la réponse devrait être 200
    Et la réponse devrait contenir "swagger"

  Scénario: Accès à l'API OpenAPI
    Étant donné que l'application I2R est démarrée
    Quand j'effectue une requête GET sur "/openapi"
    Alors le code de statut de la réponse devrait être 200
    Et la réponse devrait être un JSON valide
