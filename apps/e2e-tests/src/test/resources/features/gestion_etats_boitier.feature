# language: fr
Fonctionnalité: Gestion des états du boîtier BIP

  En tant que Système d'Information
  Je veux pouvoir changer l'état du boîtier BIP
  Afin de contrôler son cycle de vie opérationnel

  Contexte:
    Étant donné que l'application I2R est démarrée
    Et que le boîtier est à l'état "INIT"
    Et que l'IDMS du boîtier est "12345678901234567890123456789012"
    Et que le hash de configuration est "abc123def456"

  Scénario: Changement d'état réussi de INIT vers STABLE
    Quand je change l'état du boîtier vers "STABLE"
    Alors le changement d'état devrait réussir
    Et le code de statut de la réponse devrait être 200
    Et l'état du boîtier devrait être "STABLE"

  Scénario: Changement d'état réussi de STABLE vers INIT
    Étant donné que le boîtier est à l'état "STABLE"
    Quand je change l'état du boîtier vers "INIT"
    Alors le changement d'état devrait réussir
    Et le code de statut de la réponse devrait être 200
    Et l'état du boîtier devrait être "INIT"

  Scénario: Échec du changement d'état avec IDMS incorrect
    Quand je change l'état du boîtier vers "STABLE" avec l'IDMS "WRONG-IDMS"
    Alors le changement d'état devrait échouer
    Et le code de statut de la réponse devrait être 400

  Scénario: Échec du changement d'état avec hash incorrect
    Quand je change l'état du boîtier vers "STABLE" avec le hash "wrong-hash"
    Alors le changement d'état devrait échouer
    Et le code de statut de la réponse devrait être 400

  Scénario: Récupération de la configuration du boîtier
    Quand je récupère la configuration du boîtier
    Alors le code de statut de la réponse devrait être 200
    Et la configuration devrait être valide
    Et la configuration devrait contenir l'IDMS "12345678901234567890123456789012"
    Et la configuration devrait contenir le hash "abc123def456"

  Scénario: Récupération de la configuration avec profondeur spécifique
    Quand je récupère la configuration du boîtier avec la profondeur 5
    Alors le code de statut de la réponse devrait être 200
    Et la configuration devrait être valide

  Plan du scénario: Validation des états du boîtier
    Étant donné que le boîtier est à l'état "<etat_initial>"
    Quand je change l'état du boîtier vers "<nouvel_etat>"
    Alors le changement d'état devrait réussir
    Et l'état du boîtier devrait être "<nouvel_etat>"

    Exemples:
      | etat_initial | nouvel_etat |
      | INIT         | STABLE      |
      | STABLE       | INIT        |
