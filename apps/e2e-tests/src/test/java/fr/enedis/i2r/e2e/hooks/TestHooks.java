package fr.enedis.i2r.e2e.hooks;

import fr.enedis.i2r.e2e.configuration.TestConfiguration;
import fr.enedis.i2r.e2e.infrastructure.TestContext;
import fr.enedis.i2r.e2e.infrastructure.TestApplicationManager;
import io.cucumber.java.After;
import io.cucumber.java.AfterAll;
import io.cucumber.java.Before;
import io.cucumber.java.BeforeAll;
import io.cucumber.java.Scenario;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Hooks Cucumber pour la configuration globale des tests E2E.
 * Gère l'initialisation et le nettoyage avant/après les tests.
 */
public class TestHooks {

    private static final Logger logger = LoggerFactory.getLogger(TestHooks.class);

    private static boolean suiteInitialisee = false;

    @BeforeAll
    public static void avant_tous_les_tests() {
        if (!suiteInitialisee) {
            logger.info("=== DÉBUT DE LA SUITE DE TESTS E2E I2R ===");

            // Nettoyer les processus orphelins avant de commencer
            TestApplicationManager.nettoyer_processus_orphelins();

            // Charger et afficher la configuration
            TestConfiguration config = TestConfiguration.getInstance();
            config.afficher_configuration();

            // Initialiser l'environnement de test global
            initialiser_environnement_global();

            suiteInitialisee = true;
            logger.info("Suite de tests E2E initialisée avec succès");
        }
    }

    @AfterAll
    public static void apres_tous_les_tests() {
        logger.info("=== FIN DE LA SUITE DE TESTS E2E I2R ===");

        // Nettoyer les processus avant le nettoyage global
        TestApplicationManager.nettoyer_processus_orphelins();

        // Nettoyage global
        nettoyer_environnement_global();

        logger.info("Suite de tests E2E terminée");
    }

    @Before
    public void avant_chaque_scenario(Scenario scenario) {
        logger.info("--- DÉBUT DU SCÉNARIO: {} ---", scenario.getName());

        // Nettoyer le contexte de test
        TestContext.getInstance().nettoyer_donnees();

        // Marquer le début du scénario dans le contexte
        TestContext.getInstance().stocker_donnee("scenario_nom", scenario.getName());
        TestContext.getInstance().stocker_donnee("scenario_debut", System.currentTimeMillis());

        if (TestConfiguration.getInstance().isLogTestDetails()) {
            logger.debug("Scénario: {}", scenario.getName());
            logger.debug("Tags: {}", scenario.getSourceTagNames());
            logger.debug("URI: {}", scenario.getUri());
            logger.debug("Ligne: {}", scenario.getLine());
        }
    }

    @After
    public void apres_chaque_scenario(Scenario scenario) {
        Long debutScenario = TestContext.getInstance().recuperer_donnee("scenario_debut", Long.class);
        long dureeMs = debutScenario != null ? System.currentTimeMillis() - debutScenario : 0;

        String statut = scenario.isFailed() ? "ÉCHEC" : "SUCCÈS";
        logger.info("--- FIN DU SCÉNARIO: {} - {} ({}ms) ---", scenario.getName(), statut, dureeMs);

        // En cas d'échec, capturer des informations de debug
        if (scenario.isFailed()) {
            capturer_informations_echec(scenario);
        }

        // Nettoyer les applications après chaque scénario
        try {
            TestApplicationManager applicationManager = new TestApplicationManager();
            applicationManager.arreter_applications();
        } catch (Exception e) {
            logger.warn("Erreur lors du nettoyage des applications: {}", e.getMessage());
        }

        // Nettoyer le contexte après le scénario
        TestContext.nettoyer();
    }

    @Before("@smoke")
    public void avant_test_smoke(Scenario scenario) {
        logger.info("Exécution d'un test smoke: {}", scenario.getName());
    }

    @Before("@regression")
    public void avant_test_regression(Scenario scenario) {
        logger.info("Exécution d'un test de régression: {}", scenario.getName());
    }

    @Before("@slow")
    public void avant_test_lent(Scenario scenario) {
        logger.warn("Exécution d'un test lent: {} - Cela peut prendre du temps", scenario.getName());
    }

    @After("@cleanup")
    public void apres_test_avec_nettoyage(Scenario scenario) {
        logger.info("Nettoyage spécial après le test: {}", scenario.getName());
        // Nettoyage spécifique pour les tests marqués @cleanup
        effectuer_nettoyage_approfondi();
    }

    private static void initialiser_environnement_global() {
        try {
            // Créer les répertoires de test nécessaires
            creer_repertoires_test();

            // Initialiser les ressources globales
            initialiser_ressources_globales();

            logger.debug("Environnement global initialisé");
        } catch (Exception e) {
            logger.error("Erreur lors de l'initialisation de l'environnement global", e);
            throw new RuntimeException("Impossible d'initialiser l'environnement de test", e);
        }
    }

    private static void nettoyer_environnement_global() {
        try {
            // Nettoyer les ressources globales
            nettoyer_ressources_globales();

            logger.debug("Environnement global nettoyé");
        } catch (Exception e) {
            logger.warn("Erreur lors du nettoyage de l'environnement global", e);
        }
    }

    private static void creer_repertoires_test() {
        TestConfiguration config = TestConfiguration.getInstance();

        // Créer le répertoire de base de données de test
        String dbPath = config.getTestDbPath();
        java.nio.file.Path dbDir = java.nio.file.Paths.get(dbPath).getParent();
        try {
            java.nio.file.Files.createDirectories(dbDir);
            logger.debug("Répertoire de base de données créé: {}", dbDir);
        } catch (Exception e) {
            logger.warn("Impossible de créer le répertoire de base de données: {}", dbDir, e);
        }

        // Créer le répertoire de métriques de test
        String metricsPath = config.getTestMetricsPath();
        java.nio.file.Path metricsDir = java.nio.file.Paths.get(metricsPath);
        try {
            java.nio.file.Files.createDirectories(metricsDir);
            logger.debug("Répertoire de métriques créé: {}", metricsDir);
        } catch (Exception e) {
            logger.warn("Impossible de créer le répertoire de métriques: {}", metricsDir, e);
        }
    }

    private static void initialiser_ressources_globales() {
        // Initialiser les ressources partagées entre tous les tests
        logger.debug("Initialisation des ressources globales");
    }

    private static void nettoyer_ressources_globales() {
        // Nettoyer les ressources partagées
        logger.debug("Nettoyage des ressources globales");
    }

    private void capturer_informations_echec(Scenario scenario) {
        logger.error("ÉCHEC DU SCÉNARIO: {}", scenario.getName());

        // Capturer l'état du contexte de test
        TestContext context = TestContext.getInstance();

        // Informations sur la dernière réponse HTTP
        try {
            Integer codeStatut = context.recuperer_code_statut_http();
            String corpsReponse = context.recuperer_corps_reponse();
            Exception derniereException = context.recuperer_derniere_exception();

            if (codeStatut != null) {
                logger.error("Dernier code de statut HTTP: {}", codeStatut);
            }
            if (corpsReponse != null) {
                logger.error("Dernier corps de réponse: {}", corpsReponse);
            }
            if (derniereException != null) {
                logger.error("Dernière exception:", derniereException);
            }
        } catch (Exception e) {
            logger.warn("Erreur lors de la capture des informations d'échec", e);
        }

        // Attacher des informations au rapport Cucumber si possible
        if (TestConfiguration.getInstance().isLogTestDetails()) {
            String debugInfo = String.format(
                "Informations de debug:\n" +
                "- Scénario: %s\n" +
                "- URI: %s\n" +
                "- Ligne: %d\n" +
                "- Tags: %s",
                scenario.getName(),
                scenario.getUri(),
                scenario.getLine(),
                scenario.getSourceTagNames()
            );
            scenario.attach(debugInfo, "text/plain", "Debug Info");
        }
    }

    private void effectuer_nettoyage_approfondi() {
        // Nettoyage approfondi pour certains tests
        logger.debug("Nettoyage approfondi en cours...");

        // Nettoyer le contexte de test
        TestContext.getInstance().nettoyer_donnees();

        // Autres nettoyages spécifiques si nécessaire
    }
}
