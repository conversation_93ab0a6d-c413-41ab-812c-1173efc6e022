package fr.enedis.i2r.e2e.infrastructure;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.databind.ObjectMapper;

import fr.enedis.i2r.e2e.configuration.TestConfiguration;

/**
 * Client HTTP pour les tests E2E de l'application I2R.
 * Fournit des méthodes utilitaires pour interagir avec l'API REST.
 */
public class TestHttpClient {

    private static final Logger logger = LoggerFactory.getLogger(TestHttpClient.class);

    private static TestHttpClient instance;
    private final HttpClient httpClient;
    private final ObjectMapper objectMapper;
    private final TestConfiguration config;

    private TestHttpClient() {
        this.config = TestConfiguration.getInstance();
        this.httpClient = HttpClient.newBuilder()
            .connectTimeout(Duration.ofSeconds(config.getHttpRequestTimeout()))
            .build();
        this.objectMapper = new ObjectMapper();
    }

    public static synchronized TestHttpClient getInstance() {
        if (instance == null) {
            instance = new TestHttpClient();
        }
        return instance;
    }

    /**
     * Vérifie si l'application I2R est disponible.
     * Pour les tests E2E, on vérifie le SI mock à la place.
     */
    public boolean verifier_application_disponible() {
        // Pour les tests E2E, on utilise le SI mock comme proxy de l'application
        return verifier_si_mock_disponible();
    }

    /**
     * Vérifie si l'application I2R est disponible sur son port réel (8081).
     */
    public boolean verifier_application_i2r_disponible() {
        try {
            // Essayer d'abord une simple connexion TCP
            String host = "localhost";
            int port = 8081;
            logger.info("Vérification de la disponibilité de l'application I2R sur: {}:{}", host, port);

            try (java.net.Socket socket = new java.net.Socket()) {
                socket.connect(new java.net.InetSocketAddress(host, port), 2000);
                logger.info("Connexion TCP réussie à l'application I2R");
                return true;
            }
        } catch (Exception e) {
            logger.info("Erreur lors de la vérification de l'application I2R: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Vérifie si le SI mock est disponible.
     */
    public boolean verifier_si_mock_disponible() {
        try {
            // Essayer d'abord une simple connexion TCP
            String host = "localhost";
            int port = 8440;
            logger.info("Vérification de la disponibilité du SI mock sur: {}:{}", host, port);

            try (java.net.Socket socket = new java.net.Socket()) {
                socket.connect(new java.net.InetSocketAddress(host, port), 2000);
                logger.info("Connexion TCP réussie au SI mock");
                return true;
            }
        } catch (Exception e) {
            logger.info("Erreur lors de la vérification du SI mock: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Effectue une requête GET vers l'API I2R.
     * Pour les tests E2E, utilise le SI mock.
     */
    public HttpResponse<String> effectuer_requete_get(String endpoint) throws IOException, InterruptedException {
        String url = config.getSiMockUrl() + endpoint;
        logger.info("Requête GET sur: {}", url);

        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create(url))
            .GET()
            .timeout(Duration.ofSeconds(config.getHttpRequestTimeout()))
            .build();

        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

        logger.info("Réponse reçue - Code: {}, Corps: {}", response.statusCode(), response.body());
        return response;
    }

    /**
     * Effectue une requête POST vers l'API I2R.
     * Pour les tests E2E, utilise le SI mock.
     */
    public HttpResponse<String> effectuer_requete_post(String endpoint, String body, Map<String, String> headers)
            throws IOException, InterruptedException {

        String url = config.getSiMockUrl() + endpoint;
        logger.info("Requête POST sur: {} avec corps: {}", url, body);

        HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
            .uri(URI.create(url))
            .POST(HttpRequest.BodyPublishers.ofString(body))
            .timeout(Duration.ofSeconds(config.getHttpRequestTimeout()))
            .header("Content-Type", "application/json");

        if (headers != null) {
            headers.forEach(requestBuilder::header);
        }

        HttpResponse<String> response = httpClient.send(requestBuilder.build(), HttpResponse.BodyHandlers.ofString());

        logger.info("Réponse reçue - Code: {}, Corps: {}", response.statusCode(), response.body());
        return response;
    }

    /**
     * Effectue une requête PUT vers l'API I2R.
     * Pour les tests E2E, utilise le SI mock.
     */
    public HttpResponse<String> effectuer_requete_put(String endpoint, String body, Map<String, String> headers)
            throws IOException, InterruptedException {

        String url = config.getSiMockUrl() + endpoint;
        logger.info("Requête PUT sur: {} avec corps: {}", url, body);

        HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
            .uri(URI.create(url))
            .PUT(HttpRequest.BodyPublishers.ofString(body))
            .timeout(Duration.ofSeconds(config.getHttpRequestTimeout()))
            .header("Content-Type", "application/json");

        if (headers != null) {
            headers.forEach(requestBuilder::header);
        }

        HttpResponse<String> response = httpClient.send(requestBuilder.build(), HttpResponse.BodyHandlers.ofString());

        logger.info("Réponse reçue - Code: {}, Corps: {}", response.statusCode(), response.body());
        return response;
    }

    /**
     * Effectue une requête vers le SI mock.
     */
    public HttpResponse<String> effectuer_requete_si_mock(String endpoint, String method, String body, Map<String, String> headers)
            throws IOException, InterruptedException {

        HttpRequest.BodyPublisher bodyPublisher = body != null ?
            HttpRequest.BodyPublishers.ofString(body) :
            HttpRequest.BodyPublishers.noBody();

        HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
            .uri(URI.create(config.getSiMockUrl() + endpoint))
            .method(method, bodyPublisher)
            .timeout(Duration.ofSeconds(config.getHttpRequestTimeout()));

        if (body != null) {
            requestBuilder.header("Content-Type", "application/json");
        }

        if (headers != null) {
            headers.forEach(requestBuilder::header);
        }

        return httpClient.send(requestBuilder.build(), HttpResponse.BodyHandlers.ofString());
    }

    /**
     * Convertit un objet en JSON.
     */
    public String convertir_en_json(Object object) throws IOException {
        return objectMapper.writeValueAsString(object);
    }

    /**
     * Convertit du JSON en objet.
     */
    public <T> T convertir_depuis_json(String json, Class<T> clazz) throws IOException {
        return objectMapper.readValue(json, clazz);
    }

    /**
     * Obtient l'URL de base de l'application.
     */
    public String obtenir_url_base_application() {
        return config.getAppBaseUrl();
    }

    /**
     * Obtient l'URL de base du SI mock.
     */
    public String obtenir_url_base_si_mock() {
        return config.getSiMockUrl();
    }
}
