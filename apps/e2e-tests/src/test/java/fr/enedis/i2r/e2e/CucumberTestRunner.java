package fr.enedis.i2r.e2e;

import org.junit.platform.suite.api.ConfigurationParameter;
import org.junit.platform.suite.api.IncludeEngines;
import org.junit.platform.suite.api.SelectClasspathResource;
import org.junit.platform.suite.api.Suite;

import static io.cucumber.junit.platform.engine.Constants.GLUE_PROPERTY_NAME;
import static io.cucumber.junit.platform.engine.Constants.PLUGIN_PROPERTY_NAME;

/**
 * Test runner principal pour les tests Cucumber E2E de l'application I2R.
 *
 * Ce runner configure Cucumber pour :
 * - Exécuter tous les fichiers .feature dans le classpath
 * - Utiliser les step definitions dans le package fr.enedis.i2r.e2e
 * - Générer des rapports de test détaillés
 */
@Suite
@IncludeEngines("cucumber")
@SelectClasspathResource("features")
@ConfigurationParameter(key = GLUE_PROPERTY_NAME, value = "fr.enedis.i2r.e2e")
@ConfigurationParameter(key = PLUGIN_PROPERTY_NAME, value = "pretty,html:target/cucumber-reports/html,json:target/cucumber-reports/Cucumber.json,junit:target/cucumber-reports/Cucumber.xml")
public class CucumberTestRunner {
    // Cette classe sert uniquement de point d'entrée pour JUnit Platform
    // Les tests sont définis dans les fichiers .feature et les step definitions
}
