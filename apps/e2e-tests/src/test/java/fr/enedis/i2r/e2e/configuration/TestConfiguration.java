package fr.enedis.i2r.e2e.configuration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * Configuration centralisée pour les tests E2E.
 * Charge les propriétés depuis les fichiers de configuration et les variables d'environnement.
 */
public class TestConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(TestConfiguration.class);

    private static TestConfiguration instance;
    private final Properties properties;

    private TestConfiguration() {
        this.properties = new Properties();
        charger_configuration();
    }

    public static synchronized TestConfiguration getInstance() {
        if (instance == null) {
            instance = new TestConfiguration();
        }
        return instance;
    }

    private void charger_configuration() {
        // Charger les propriétés par défaut
        charger_proprietes_par_defaut();

        // Charger depuis le fichier de configuration si présent
        charger_depuis_fichier("test-config.properties");

        // Surcharger avec les variables d'environnement
        charger_variables_environnement();

        logger.info("Configuration des tests E2E chargée");
    }

    private void charger_proprietes_par_defaut() {
        // URLs par défaut
        properties.setProperty("app.base.url", "http://localhost:8081");
        properties.setProperty("si.mock.url", "http://localhost:8440");

        // Timeouts par défaut (en secondes)
        properties.setProperty("app.startup.timeout", "60");
        properties.setProperty("si.startup.timeout", "30");
        properties.setProperty("http.request.timeout", "10");

        // Chemins de test
        properties.setProperty("test.db.path", "target/test-data/i2r-params-e2e.db");
        properties.setProperty("test.metrics.path", "target/test-data/metrics");

        // Données de test par défaut
        properties.setProperty("test.idms", "12345678901234567890123456789012"); // 32 characters as required by SI mock
        properties.setProperty("test.config.hash", "abc123def456");
        properties.setProperty("test.api.version", "1.0");

        // Configuration des logs
        properties.setProperty("log.level", "INFO");
        properties.setProperty("log.test.details", "true");
    }

    private void charger_depuis_fichier(String nomFichier) {
        try (InputStream is = getClass().getClassLoader().getResourceAsStream(nomFichier)) {
            if (is != null) {
                Properties fileProps = new Properties();
                fileProps.load(is);
                properties.putAll(fileProps);
                logger.debug("Propriétés chargées depuis {}", nomFichier);
            }
        } catch (IOException e) {
            logger.warn("Impossible de charger le fichier de configuration {}: {}", nomFichier, e.getMessage());
        }
    }

    private void charger_variables_environnement() {
        // Mapper les variables d'environnement vers les propriétés
        mapperVariableEnvironnement("I2R_APP_URL", "app.base.url");
        mapperVariableEnvironnement("I2R_SI_MOCK_URL", "si.mock.url");
        mapperVariableEnvironnement("I2R_TEST_DB_PATH", "test.db.path");
        mapperVariableEnvironnement("I2R_TEST_METRICS_PATH", "test.metrics.path");
        mapperVariableEnvironnement("I2R_TEST_IDMS", "test.idms");
        mapperVariableEnvironnement("I2R_TEST_CONFIG_HASH", "test.config.hash");
        mapperVariableEnvironnement("I2R_LOG_LEVEL", "log.level");
    }

    private void mapperVariableEnvironnement(String variableEnv, String propriete) {
        String valeur = System.getenv(variableEnv);
        if (valeur != null && !valeur.trim().isEmpty()) {
            properties.setProperty(propriete, valeur);
            logger.debug("Variable d'environnement {} mappée vers {}: {}", variableEnv, propriete, valeur);
        }
    }

    // Méthodes d'accès aux propriétés

    public String getAppBaseUrl() {
        return properties.getProperty("app.base.url");
    }

    public String getSiMockUrl() {
        return properties.getProperty("si.mock.url");
    }

    public int getAppStartupTimeout() {
        return Integer.parseInt(properties.getProperty("app.startup.timeout"));
    }

    public int getSiStartupTimeout() {
        return Integer.parseInt(properties.getProperty("si.startup.timeout"));
    }

    public int getHttpRequestTimeout() {
        return Integer.parseInt(properties.getProperty("http.request.timeout"));
    }

    public String getTestDbPath() {
        return properties.getProperty("test.db.path");
    }

    public String getTestMetricsPath() {
        return properties.getProperty("test.metrics.path");
    }

    public String getTestIdms() {
        return properties.getProperty("test.idms");
    }

    public String getTestConfigHash() {
        return properties.getProperty("test.config.hash");
    }

    public String getTestApiVersion() {
        return properties.getProperty("test.api.version");
    }

    public String getLogLevel() {
        return properties.getProperty("log.level");
    }

    public boolean isLogTestDetails() {
        return Boolean.parseBoolean(properties.getProperty("log.test.details"));
    }

    public String getProperty(String key) {
        return properties.getProperty(key);
    }

    public String getProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }

    public void setProperty(String key, String value) {
        properties.setProperty(key, value);
    }

    /**
     * Affiche toutes les propriétés de configuration (pour debug).
     */
    public void afficher_configuration() {
        if (isLogTestDetails()) {
            logger.info("=== Configuration des tests E2E ===");
            properties.entrySet().stream()
                .sorted((e1, e2) -> e1.getKey().toString().compareTo(e2.getKey().toString()))
                .forEach(entry -> logger.info("{} = {}", entry.getKey(), entry.getValue()));
            logger.info("===================================");
        }
    }
}
