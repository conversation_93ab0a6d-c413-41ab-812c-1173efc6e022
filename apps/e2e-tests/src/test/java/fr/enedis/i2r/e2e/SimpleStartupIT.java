package fr.enedis.i2r.e2e;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Test simple pour vérifier le démarrage du SI mock.
 */
public class SimpleStartupIT {

    private static final Logger logger = LoggerFactory.getLogger(SimpleStartupIT.class);

    @Test
    void test_simple_si_mock_startup() throws Exception {
        logger.info("=== Test simple de démarrage du SI mock ===");

        // Vérifier que le JAR existe
        String[] cheminsPossibles = {
            "../si-mock/target/si-mock-1.0.1-SNAPSHOT-jar-with-dependencies.jar",
            "apps/si-mock/target/si-mock-1.0.1-SNAPSHOT-jar-with-dependencies.jar",
            "si-mock/target/si-mock-1.0.1-SNAPSHOT-jar-with-dependencies.jar"
        };

        String jarPath = null;
        for (String chemin : cheminsPossibles) {
            Path path = Paths.get(chemin);
            logger.info("Vérification du chemin: {}", path.toAbsolutePath());
            if (Files.exists(path)) {
                jarPath = path.toAbsolutePath().toString();
                logger.info("JAR trouvé: {}", jarPath);
                break;
            }
        }

        if (jarPath == null) {
            throw new RuntimeException("JAR du SI mock non trouvé");
        }

        // Démarrer le SI mock
        logger.info("Démarrage du SI mock...");
        ProcessBuilder pb = new ProcessBuilder("java", "-jar", jarPath);
        pb.redirectOutput(Paths.get("target/test-data/simple-si-mock.log").toFile());
        pb.redirectError(Paths.get("target/test-data/simple-si-mock-error.log").toFile());

        Process process = pb.start();
        logger.info("SI mock démarré avec PID: {}", process.pid());

        // Attendre que le SI mock démarre
        logger.info("Attente du démarrage du SI mock...");
        Thread.sleep(10000);

        // Tester la connexion
        HttpClient httpClient = HttpClient.newHttpClient();
        try {
            HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://localhost:8440/swagger"))
                .GET()
                .timeout(Duration.ofSeconds(5))
                .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            logger.info("Réponse du SI mock: status={}", response.statusCode());

            if (response.statusCode() == 200) {
                logger.info("SI mock accessible!");
            } else {
                logger.error("SI mock non accessible, status: {}", response.statusCode());
            }
        } catch (Exception e) {
            logger.error("Erreur lors de la connexion au SI mock: {}", e.getMessage());
        } finally {
            // Arrêter le processus
            process.destroy();
            logger.info("Processus SI mock arrêté");
        }
    }
}
