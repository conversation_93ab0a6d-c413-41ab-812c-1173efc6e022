package fr.enedis.i2r.e2e.infrastructure;

import java.net.http.HttpResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * Contexte partagé entre les étapes des tests Cucumber.
 * Permet de stocker et récupérer des données entre les différentes étapes d'un scénario.
 */
public class TestContext {

    private static final ThreadLocal<TestContext> context = ThreadLocal.withInitial(TestContext::new);

    private final Map<String, Object> donnees = new HashMap<>();
    private HttpResponse<String> derniereReponseHttp;
    private Exception derniereException;
    private Map<String, String> entetesHttp;

    /**
     * Obtient l'instance du contexte pour le thread actuel.
     */
    public static TestContext getInstance() {
        return context.get();
    }

    /**
     * Nettoie le contexte pour le thread actuel.
     */
    public static void nettoyer() {
        context.remove();
    }

    /**
     * Stocke une donnée dans le contexte.
     */
    public void stocker_donnee(String cle, Object valeur) {
        donnees.put(cle, valeur);
    }

    /**
     * Récupère une donnée du contexte.
     */
    @SuppressWarnings("unchecked")
    public <T> T recuperer_donnee(String cle, Class<T> type) {
        Object valeur = donnees.get(cle);
        if (valeur == null) {
            return null;
        }

        if (type.isInstance(valeur)) {
            return (T) valeur;
        }

        throw new ClassCastException(
            String.format("La donnée '%s' n'est pas du type attendu %s mais %s",
                cle, type.getSimpleName(), valeur.getClass().getSimpleName())
        );
    }

    /**
     * Récupère une donnée du contexte avec une valeur par défaut.
     */
    public <T> T recuperer_donnee_ou_defaut(String cle, Class<T> type, T valeurParDefaut) {
        T valeur = recuperer_donnee(cle, type);
        return valeur != null ? valeur : valeurParDefaut;
    }

    /**
     * Vérifie si une donnée existe dans le contexte.
     */
    public boolean contient_donnee(String cle) {
        return donnees.containsKey(cle);
    }

    /**
     * Supprime une donnée du contexte.
     */
    public void supprimer_donnee(String cle) {
        donnees.remove(cle);
    }

    /**
     * Stocke la dernière réponse HTTP reçue.
     */
    public void stocker_derniere_reponse_http(HttpResponse<String> reponse) {
        this.derniereReponseHttp = reponse;
    }

    /**
     * Récupère la dernière réponse HTTP.
     */
    public HttpResponse<String> recuperer_derniere_reponse_http() {
        return derniereReponseHttp;
    }

    /**
     * Stocke la dernière exception survenue.
     */
    public void stocker_derniere_exception(Exception exception) {
        this.derniereException = exception;
    }

    /**
     * Récupère la dernière exception.
     */
    public Exception recuperer_derniere_exception() {
        return derniereException;
    }

    /**
     * Nettoie toutes les données du contexte.
     */
    public void nettoyer_donnees() {
        donnees.clear();
        derniereReponseHttp = null;
        derniereException = null;
        entetesHttp = null;
    }

    // Méthodes utilitaires pour les données couramment utilisées

    public void stocker_etat_boitier(String etat) {
        stocker_donnee("etat_boitier", etat);
    }

    public String recuperer_etat_boitier() {
        return recuperer_donnee("etat_boitier", String.class);
    }

    public void stocker_idms(String idms) {
        stocker_donnee("idms", idms);
    }

    public String recuperer_idms() {
        return recuperer_donnee("idms", String.class);
    }

    public void stocker_hash_configuration(String hash) {
        stocker_donnee("hash_configuration", hash);
    }

    public String recuperer_hash_configuration() {
        return recuperer_donnee("hash_configuration", String.class);
    }

    public void stocker_niveau_log(String niveau) {
        stocker_donnee("niveau_log", niveau);
    }

    public String recuperer_niveau_log() {
        return recuperer_donnee("niveau_log", String.class);
    }

    public void stocker_code_statut_http(int code) {
        stocker_donnee("code_statut_http", code);
    }

    public Integer recuperer_code_statut_http() {
        return recuperer_donnee("code_statut_http", Integer.class);
    }

    public void stocker_corps_reponse(String corps) {
        stocker_donnee("corps_reponse", corps);
    }

    public String recuperer_corps_reponse() {
        return recuperer_donnee("corps_reponse", String.class);
    }

    // Nouvelles méthodes pour les step definitions
    public void definir_etat_boitier(String etat) {
        stocker_etat_boitier(etat);
    }

    public void definir_idms(String idms) {
        stocker_idms(idms);
    }

    public void definir_hash_configuration(String hash) {
        stocker_hash_configuration(hash);
    }

    public void definir_datacenter_principal_indisponible(boolean indisponible) {
        stocker_donnee("datacenter_principal_indisponible", indisponible);
    }

    public boolean est_datacenter_principal_indisponible() {
        return recuperer_donnee_ou_defaut("datacenter_principal_indisponible", Boolean.class, false);
    }

    /**
     * Stocke les en-têtes HTTP pour les utiliser dans les étapes suivantes.
     */
    public void stocker_entetes_http(Map<String, String> entetes) {
        this.entetesHttp = entetes;
    }

    /**
     * Récupère les en-têtes HTTP stockés.
     */
    public Map<String, String> recuperer_entetes_http() {
        return entetesHttp;
    }
}
