package fr.enedis.i2r.e2e.steps;

import static org.assertj.core.api.Assertions.assertThat;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.e2e.infrastructure.TestApplicationManager;
import fr.enedis.i2r.e2e.infrastructure.TestContext;
import fr.enedis.i2r.e2e.infrastructure.TestHttpClient;
import io.cucumber.java.After;
import io.cucumber.java.Before;
import io.cucumber.java.fr.Alors;
import io.cucumber.java.fr.Quand;
import io.cucumber.java.fr.Étantdonnéque;

public class ApplicationSteps {

    private static final Logger logger = LoggerFactory.getLogger(ApplicationSteps.class);

    private final TestApplicationManager applicationManager = new TestApplicationManager();
    private final TestHttpClient httpClient = TestHttpClient.getInstance();
    private final TestContext context = TestContext.getInstance();

    @Before
    public void avant_chaque_scenario() {
        logger.debug("Initialisation du contexte de test");
        context.nettoyer_donnees();
    }

    @After
    public void apres_chaque_scenario() {
        logger.debug("Nettoyage après le scénario");
        try {
            applicationManager.arreter_application_complete();
        } catch (Exception e) {
            logger.warn("Erreur lors de l'arrêt de l'application", e);
        }
        TestContext.nettoyer();
    }

    @Étantdonnéque("l'application I2R est démarrée")
    public void que_l_application_i2r_est_demarree() throws Exception {
        logger.info("Démarrage du SI mock pour les tests E2E");
        applicationManager.demarrer_si_mock_seulement();

        // Vérifier que le SI mock est accessible
        boolean accessible = httpClient.verifier_si_mock_disponible();
        assertThat(accessible)
            .as("Le SI mock devrait être accessible")
            .isTrue();

        logger.info("SI mock démarré et accessible pour les tests E2E");
    }

    @Étantdonnéque("l'application I2R est arrêtée")
    public void que_l_application_i2r_est_arretee() {
        logger.info("Arrêt du SI mock");
        applicationManager.arreter_applications();

        // Vérifier que le SI mock n'est plus accessible
        boolean accessible = httpClient.verifier_si_mock_disponible();
        assertThat(accessible)
            .as("Le SI mock ne devrait plus être accessible")
            .isFalse();
    }

    @Quand("je démarre l'application I2R")
    public void je_demarre_l_application_i2r() throws Exception {
        logger.info("Démarrage du SI mock");
        applicationManager.demarrer_si_mock_seulement();
    }

    @Quand("j'arrête l'application I2R")
    public void j_arrete_l_application_i2r() {
        logger.info("Arrêt du SI mock");
        applicationManager.arreter_applications();
    }

    @Alors("l'application I2R devrait être en cours d'exécution")
    public void l_application_i2r_devrait_etre_en_cours_d_execution() {
        assertThat(applicationManager.est_application_demarree())
            .as("L'application I2R devrait être en cours d'exécution")
            .isTrue();
    }

    @Alors("l'application I2R devrait être arrêtée")
    public void l_application_i2r_devrait_etre_arretee() {
        assertThat(applicationManager.est_application_demarree())
            .as("L'application I2R devrait être arrêtée")
            .isFalse();
    }

    @Alors("l'API REST devrait être accessible")
    public void l_api_rest_devrait_etre_accessible() {
        assertThat(httpClient.verifier_application_disponible())
            .as("L'API REST devrait être accessible")
            .isTrue();
    }

    @Alors("le SI mock devrait être accessible")
    public void le_si_mock_devrait_etre_accessible() {
        assertThat(httpClient.verifier_si_mock_disponible())
            .as("Le SI mock devrait être accessible")
            .isTrue();
    }

    @Étantdonnéque("le SI mock est démarré")
    public void que_le_si_mock_est_demarre() {
        // Le SI mock est démarré automatiquement avec l'application
        assertThat(httpClient.verifier_si_mock_disponible())
            .as("Le SI mock devrait être accessible")
            .isTrue();
    }

    @Quand("j'attends {int} secondes")
    public void j_attends_secondes(int secondes) throws InterruptedException {
        logger.debug("Attente de {} secondes", secondes);
        Thread.sleep(secondes * 1000L);
    }

    @Quand("j'attends {int} millisecondes")
    public void j_attends_millisecondes(int millisecondes) throws InterruptedException {
        logger.debug("Attente de {} millisecondes", millisecondes);
        Thread.sleep(millisecondes);
    }

    // Step definitions communes utilisées dans plusieurs features
    @Étantdonnéque("le SI est accessible")
    public void que_le_si_est_accessible() {
        logger.info("Vérification que le SI est accessible");
        assertThat(httpClient.verifier_si_mock_disponible())
            .as("Le SI devrait être accessible")
            .isTrue();
    }

    @Étantdonnéque("le boîtier est à l'état {string}")
    public void que_le_boitier_est_a_l_etat(String etat) {
        logger.info("Configuration du boîtier à l'état: {}", etat);
        context.definir_etat_boitier(etat);
    }

    @Étantdonnéque("l'IDMS du boîtier est {string}")
    public void que_l_idms_du_boitier_est(String idms) {
        logger.info("Configuration de l'IDMS du boîtier: {}", idms);
        context.definir_idms(idms);
    }

    @Étantdonnéque("le hash de configuration est {string}")
    public void que_le_hash_de_configuration_est(String hash) {
        logger.info("Configuration du hash de configuration: {}", hash);
        context.definir_hash_configuration(hash);
    }

    @Étantdonnéque("le datacenter principal est indisponible")
    public void que_le_datacenter_principal_est_indisponible() {
        logger.info("Simulation de l'indisponibilité du datacenter principal");
        context.definir_datacenter_principal_indisponible(true);
    }

    /**
     * Nettoie les applications après chaque scénario.
     * Cette méthode est appelée automatiquement par les hooks.
     */
    public void nettoyer_applications() {
        try {
            logger.debug("Nettoyage des applications après le scénario");
            applicationManager.arreter_applications();
        } catch (Exception e) {
            logger.warn("Erreur lors du nettoyage des applications: {}", e.getMessage());
        }
    }
}
