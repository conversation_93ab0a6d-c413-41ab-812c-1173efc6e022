package fr.enedis.i2r.e2e.steps;

import static org.assertj.core.api.Assertions.assertThat;

import java.net.http.HttpResponse;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.e2e.infrastructure.TestContext;
import fr.enedis.i2r.e2e.infrastructure.TestHttpClient;
import io.cucumber.java.fr.Alors;
import io.cucumber.java.fr.Quand;

public class BoitierSteps {

    private static final Logger logger = LoggerFactory.getLogger(BoitierSteps.class);

    private final TestHttpClient httpClient = TestHttpClient.getInstance();
    private final TestContext context = TestContext.getInstance();

    // Constantes pour les états du boîtier
    private static final String ETAT_INIT = "INIT";
    private static final String ETAT_STABLE = "STABLE";
    private static final int CODE_ETAT_INIT = 1;
    private static final int CODE_ETAT_STABLE = 2;
    private static final String API_VERSION_TEST = "2.0";

    // Les step definitions communes sont maintenant dans ApplicationSteps

    @Quand("je change l'état du boîtier vers {string}")
    public void je_change_l_etat_du_boitier_vers(String nouvelEtat) throws Exception {
        logger.info("Changement de l'état du boîtier vers {}", nouvelEtat);

        int codeEtat = convertir_etat_vers_code(nouvelEtat);
        String idms = context.recuperer_idms();
        String hashConfig = context.recuperer_hash_configuration();

        // Construire le corps de la requête
        String corpsRequete = String.format("{\"state\": %d}", codeEtat);

        // Construire les en-têtes
        Map<String, String> entetes = new HashMap<>();
        entetes.put("X-ERDF-API-VERSION", API_VERSION_TEST);
        entetes.put("x-idms", idms);
        entetes.put("X-ERDF-HASH", hashConfig);

        try {
            HttpResponse<String> response = httpClient.effectuer_requete_put("/db/cfg/dm", corpsRequete, entetes);
            context.stocker_derniere_reponse_http(response);
            context.stocker_code_statut_http(response.statusCode());
            context.stocker_corps_reponse(response.body());

            // Si la requête réussit, mettre à jour l'état dans le contexte
            if (response.statusCode() == 200) {
                context.stocker_etat_boitier(nouvelEtat);
            }

            logger.debug("Réponse du changement d'état: status={}, body={}", response.statusCode(), response.body());
        } catch (Exception e) {
            context.stocker_derniere_exception(e);
            throw e;
        }
    }

    @Quand("je change l'état du boîtier vers {string} avec l'IDMS {string}")
    public void je_change_l_etat_du_boitier_vers_avec_l_idms(String nouvelEtat, String idms) throws Exception {
        logger.info("Changement de l'état du boîtier vers {} avec IDMS {}", nouvelEtat, idms);

        // Mettre à jour l'IDMS dans le contexte
        context.stocker_idms(idms);

        // Utiliser la méthode principale
        je_change_l_etat_du_boitier_vers(nouvelEtat);
    }

    @Quand("je change l'état du boîtier vers {string} avec le hash {string}")
    public void je_change_l_etat_du_boitier_vers_avec_le_hash(String nouvelEtat, String hash) throws Exception {
        logger.info("Changement de l'état du boîtier vers {} avec hash {}", nouvelEtat, hash);

        // Mettre à jour le hash dans le contexte
        context.stocker_hash_configuration(hash);

        // Utiliser la méthode principale
        je_change_l_etat_du_boitier_vers(nouvelEtat);
    }

    @Quand("je récupère la configuration du boîtier")
    public void je_recupere_la_configuration_du_boitier() throws Exception {
        logger.info("Récupération de la configuration du boîtier");

        try {
            HttpResponse<String> response = httpClient.effectuer_requete_get("/db/cfg");
            context.stocker_derniere_reponse_http(response);
            context.stocker_code_statut_http(response.statusCode());
            context.stocker_corps_reponse(response.body());

            logger.debug("Configuration récupérée: status={}, body={}", response.statusCode(), response.body());
        } catch (Exception e) {
            context.stocker_derniere_exception(e);
            throw e;
        }
    }

    @Quand("je récupère la configuration du boîtier avec la profondeur {int}")
    public void je_recupere_la_configuration_du_boitier_avec_la_profondeur(int profondeur) throws Exception {
        logger.info("Récupération de la configuration du boîtier avec profondeur {}", profondeur);

        try {
            String endpoint = "/db/cfg?depth=" + profondeur;
            HttpResponse<String> response = httpClient.effectuer_requete_get(endpoint);
            context.stocker_derniere_reponse_http(response);
            context.stocker_code_statut_http(response.statusCode());
            context.stocker_corps_reponse(response.body());

            logger.debug("Configuration récupérée: status={}, body={}", response.statusCode(), response.body());
        } catch (Exception e) {
            context.stocker_derniere_exception(e);
            throw e;
        }
    }

    @Alors("l'état du boîtier devrait être {string}")
    public void l_etat_du_boitier_devrait_etre(String etatAttendu) throws Exception {
        // Get the current device state from the SI mock
        HttpResponse<String> response = httpClient.effectuer_requete_si_mock("/bip/state", "GET", null, null);

        if (response.statusCode() != 200) {
            throw new AssertionError("Failed to get device state: " + response.body());
        }

        // Parse the JSON response to get the state name
        String responseBody = response.body();
        logger.debug("Device state response: {}", responseBody);

        // Extract stateName from JSON response like {"state": 2, "stateName": "STABLE"}
        String etatActuel = responseBody.replaceAll(".*\"stateName\"\\s*:\\s*\"([^\"]+)\".*", "$1");

        assertThat(etatActuel)
            .as("L'état du boîtier")
            .isEqualTo(etatAttendu);
    }

    @Alors("le changement d'état devrait réussir")
    public void le_changement_d_etat_devrait_reussir() {
        Integer codeStatut = context.recuperer_code_statut_http();
        assertThat(codeStatut)
            .as("Le code de statut du changement d'état")
            .isEqualTo(200);
    }

    @Alors("le changement d'état devrait échouer")
    public void le_changement_d_etat_devrait_echouer() {
        Integer codeStatut = context.recuperer_code_statut_http();
        assertThat(codeStatut)
            .as("Le code de statut du changement d'état")
            .isNotEqualTo(200);
    }

    @Alors("le changement d'état devrait échouer avec le code {int}")
    public void le_changement_d_etat_devrait_echouer_avec_le_code(int codeAttendu) {
        Integer codeStatut = context.recuperer_code_statut_http();
        assertThat(codeStatut)
            .as("Le code de statut du changement d'état")
            .isEqualTo(codeAttendu);
    }

    @Alors("la configuration devrait contenir l'IDMS {string}")
    public void la_configuration_devrait_contenir_l_idms(String idmsAttendu) {
        String corpsReponse = context.recuperer_corps_reponse();
        assertThat(corpsReponse)
            .as("La configuration du boîtier")
            .contains(idmsAttendu);
    }

    @Alors("la configuration devrait contenir le hash {string}")
    public void la_configuration_devrait_contenir_le_hash(String hashAttendu) {
        String corpsReponse = context.recuperer_corps_reponse();
        assertThat(corpsReponse)
            .as("La configuration du boîtier")
            .contains(hashAttendu);
    }

    @Alors("la configuration devrait être valide")
    public void la_configuration_devrait_etre_valide() throws Exception {
        String corpsReponse = context.recuperer_corps_reponse();
        assertThat(corpsReponse)
            .as("La configuration du boîtier ne devrait pas être vide")
            .isNotEmpty();

        // Vérifier que c'est un JSON valide
        try {
            httpClient.convertir_depuis_json(corpsReponse, Object.class);
        } catch (Exception e) {
            throw new AssertionError("La configuration n'est pas un JSON valide: " + corpsReponse, e);
        }
    }

    /**
     * Convertit un nom d'état en code numérique.
     */
    private int convertir_etat_vers_code(String etat) {
        return switch (etat.toUpperCase()) {
            case ETAT_INIT -> CODE_ETAT_INIT;
            case ETAT_STABLE -> CODE_ETAT_STABLE;
            default -> throw new IllegalArgumentException("État inconnu: " + etat);
        };
    }
}
