package fr.enedis.i2r.e2e.infrastructure;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.e2e.configuration.TestConfiguration;

/**
 * Gestionnaire pour démarrer et arrêter l'application I2R et le SI mock
 * pour les tests E2E.
 */
public class TestApplicationManager {

    private static final Logger logger = LoggerFactory.getLogger(TestApplicationManager.class);

    private final TestConfiguration config = TestConfiguration.getInstance();

    private Process mainAppProcess;
    private Process siMockProcess;
    private boolean isRunning = false;

    /**
     * Démarre l'application I2R et le SI mock pour les tests E2E.
     */
    public void demarrer_application_complete() throws Exception {
        if (isRunning) {
            logger.warn("L'application est déjà démarrée");
            return;
        }

        logger.info("Démarrage de l'environnement de test E2E...");

        // Préparer l'environnement de test
        preparer_environnement_test();

        // Démarrer le SI mock en premier
        logger.info("=== ÉTAPE 1: Démarrage du SI mock ===");
        demarrer_si_mock();

        // Attendre que le SI mock soit prêt
        logger.info("=== ÉTAPE 2: Attente que le SI mock soit prêt ===");
        attendre_si_mock_pret();

        // Pour les tests E2E, on ne démarre pas l'application principale car elle nécessite HAL
        // On se contente du SI mock qui est suffisant pour tester les API REST
        logger.info("=== ÉTAPE 3: Application principale non démarrée (HAL non disponible en test) ===");
        logger.info("Les tests E2E utiliseront uniquement le SI mock sur le port 8440");

        isRunning = true;
        logger.info("Environnement de test E2E démarré avec succès (SI mock uniquement)");
    }

    /**
     * Démarre uniquement le SI mock pour les tests.
     */
    public void demarrer_si_mock_seulement() throws Exception {
        if (isRunning) {
            logger.warn("L'application est déjà démarrée");
            return;
        }

        logger.info("Démarrage du SI mock pour les tests...");

        // Préparer l'environnement de test
        preparer_environnement_test();

        // Démarrer le SI mock
        demarrer_si_mock();

        // Attendre que le SI mock soit prêt
        attendre_si_mock_pret();

        isRunning = true;
        logger.info("SI mock démarré avec succès");
    }

    /**
     * Arrête l'application I2R et le SI mock.
     */
    public void arreter_application_complete() {
        if (!isRunning) {
            return;
        }

        logger.info("Arrêt de l'environnement de test E2E...");

        // Arrêter l'application principale
        if (mainAppProcess != null && mainAppProcess.isAlive()) {
            mainAppProcess.destroy();
            try {
                mainAppProcess.waitFor(10, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                mainAppProcess.destroyForcibly();
            }
        }

        // Arrêter le SI mock
        if (siMockProcess != null && siMockProcess.isAlive()) {
            siMockProcess.destroy();
            try {
                siMockProcess.waitFor(10, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                siMockProcess.destroyForcibly();
            }
        }

        isRunning = false;
        logger.info("Environnement de test E2E arrêté");
    }

    /**
     * Vérifie si l'application est en cours d'exécution.
     * En mode SI mock seulement, vérifie uniquement le SI mock.
     */
    public boolean est_application_demarree() {
        if (mainAppProcess == null) {
            // Mode SI mock seulement
            return isRunning && siMockProcess != null && siMockProcess.isAlive();
        } else {
            // Mode complet avec main app et SI mock
            return isRunning &&
                   mainAppProcess != null && mainAppProcess.isAlive() &&
                   siMockProcess != null && siMockProcess.isAlive();
        }
    }

    private void preparer_environnement_test() throws IOException {
        // Créer les répertoires de test
        Path testDataDir = Paths.get("target/test-data");
        Files.createDirectories(testDataDir);

        Path metricsDir = Paths.get(config.getTestMetricsPath());
        Files.createDirectories(metricsDir);

        // Copier la base de données de test si elle n'existe pas
        Path testDbPath = Paths.get(config.getTestDbPath());
        if (!Files.exists(testDbPath)) {
            // Créer une base de données de test vide ou copier depuis les ressources
            Files.createFile(testDbPath);
        }
    }

    private void demarrer_si_mock() throws IOException {
        logger.info("=== DÉBUT DÉMARRAGE SI MOCK ===");

        // Déterminer le chemin vers le JAR du SI mock
        String siMockJarPath = determiner_chemin_jar_si_mock();
        logger.info("Démarrage du SI mock avec le JAR: {}", siMockJarPath);

        ProcessBuilder pb = new ProcessBuilder(
            "java", "-jar", siMockJarPath
        );

        // Définir le répertoire de travail à la racine du projet
        pb.directory(Paths.get("../..").toFile());
        pb.redirectErrorStream(true);

        // Rediriger les logs vers des fichiers pour debug
        pb.redirectOutput(Paths.get("target/test-data/si-mock.log").toFile());
        pb.redirectError(Paths.get("target/test-data/si-mock-error.log").toFile());

        siMockProcess = pb.start();
        logger.info("SI mock démarré avec PID: {}", siMockProcess.pid());
        logger.info("=== FIN DÉMARRAGE SI MOCK ===");
    }

    private void demarrer_application_principale() throws IOException {
        logger.debug("Démarrage de l'application principale...");

        // Déterminer le chemin vers le JAR de l'application principale
        String mainAppJarPath = determiner_chemin_jar_main();
        logger.info("Démarrage de l'application principale avec le JAR: {}", mainAppJarPath);

        ProcessBuilder pb = new ProcessBuilder(
            "java", "-jar", mainAppJarPath
        );
        pb.environment().put("I2R_DB_PATH", config.getTestDbPath());
        pb.environment().put("I2R_METRICS_PATH", config.getTestMetricsPath());
        pb.environment().put("I2R_SI_URL", config.getSiMockUrl());
        pb.environment().put("I2R_PORT", "8081");

        // Rediriger les logs vers des fichiers pour debug
        pb.redirectOutput(Paths.get("target/test-data/main-app.log").toFile());
        pb.redirectError(Paths.get("target/test-data/main-app-error.log").toFile());

        mainAppProcess = pb.start();
        logger.debug("Application principale démarrée avec PID: {}", mainAppProcess.pid());
    }

    private void attendre_si_mock_pret() throws Exception {
        logger.info("Attente que le SI mock soit prêt...");

        int timeoutSeconds = config.getSiStartupTimeout();
        for (int i = 0; i < timeoutSeconds; i++) {
            try {
                // Tenter une connexion au SI mock
                logger.info("Tentative de connexion au SI mock (tentative {}/{})", i + 1, timeoutSeconds);
                if (TestHttpClient.getInstance().verifier_si_mock_disponible()) {
                    logger.info("SI mock prêt après {} secondes", i + 1);
                    return;
                }
            } catch (Exception e) {
                logger.info("Erreur lors de la connexion au SI mock: {}", e.getMessage());
            }

            Thread.sleep(1000);
        }

        throw new RuntimeException("Le SI mock n'a pas démarré dans les temps (" + timeoutSeconds + "s)");
    }

    private void attendre_application_prete() throws Exception {
        logger.debug("Attente que l'application principale soit prête...");

        int timeoutSeconds = config.getAppStartupTimeout();
        for (int i = 0; i < timeoutSeconds; i++) {
            try {
                // Tenter une connexion à l'API de l'application
                if (TestHttpClient.getInstance().verifier_application_disponible()) {
                    logger.debug("Application principale prête");
                    return;
                }
            } catch (Exception e) {
                // Ignorer les erreurs de connexion pendant le démarrage
            }

            Thread.sleep(1000);
        }

        throw new RuntimeException("L'application principale n'a pas démarré dans les temps (" + timeoutSeconds + "s)");
    }

    /**
     * Détermine le chemin vers le JAR du SI mock.
     */
    private String determiner_chemin_jar_si_mock() {
        // Essayer plusieurs chemins possibles pour le JAR exécutable
        String[] cheminsPossibles = {
            "../si-mock/target/si-mock-1.0.1-SNAPSHOT-jar-with-dependencies.jar",  // Depuis apps/e2e-tests
            "apps/si-mock/target/si-mock-1.0.1-SNAPSHOT-jar-with-dependencies.jar", // Depuis la racine du projet
            "si-mock/target/si-mock-1.0.1-SNAPSHOT-jar-with-dependencies.jar"       // Depuis apps/
        };

        for (String chemin : cheminsPossibles) {
            Path jarPath = Paths.get(chemin);
            if (Files.exists(jarPath)) {
                logger.debug("JAR du SI mock trouvé: {}", jarPath.toAbsolutePath());
                return jarPath.toAbsolutePath().toString();
            }
        }

        throw new RuntimeException("Impossible de trouver le JAR du SI mock. Vérifiez que 'mvn package' a été exécuté.");
    }

    /**
     * Détermine le chemin vers le JAR de l'application principale.
     */
    private String determiner_chemin_jar_main() {
        // Essayer plusieurs chemins possibles pour le JAR exécutable
        String[] cheminsPossibles = {
            "../main/target/main-1.0.1-SNAPSHOT-jar-with-dependencies.jar",  // Depuis apps/e2e-tests
            "apps/main/target/main-1.0.1-SNAPSHOT-jar-with-dependencies.jar", // Depuis la racine du projet
            "main/target/main-1.0.1-SNAPSHOT-jar-with-dependencies.jar"       // Depuis apps/
        };

        for (String chemin : cheminsPossibles) {
            Path jarPath = Paths.get(chemin);
            if (Files.exists(jarPath)) {
                logger.debug("JAR de l'application principale trouvé: {}", jarPath.toAbsolutePath());
                return jarPath.toAbsolutePath().toString();
            }
        }

        throw new RuntimeException("Impossible de trouver le JAR de l'application principale. Vérifiez que 'mvn package' a été exécuté.");
    }

    /**
     * Arrête toutes les applications de test.
     */
    public void arreter_applications() {
        logger.info("Arrêt des applications de test...");

        // Arrêter d'abord l'application principale
        if (mainAppProcess != null && mainAppProcess.isAlive()) {
            logger.info("Arrêt de l'application principale (PID: {})...", mainAppProcess.pid());
            mainAppProcess.destroy();
            try {
                if (!mainAppProcess.waitFor(5, TimeUnit.SECONDS)) {
                    logger.warn("Arrêt forcé de l'application principale");
                    mainAppProcess.destroyForcibly();
                    mainAppProcess.waitFor(2, TimeUnit.SECONDS);
                }
                logger.info("Application principale arrêtée");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.error("Interruption lors de l'arrêt de l'application principale", e);
            }
        }

        // Ensuite arrêter le SI mock
        if (siMockProcess != null && siMockProcess.isAlive()) {
            logger.info("Arrêt du SI mock (PID: {})...", siMockProcess.pid());
            siMockProcess.destroy();
            try {
                if (!siMockProcess.waitFor(5, TimeUnit.SECONDS)) {
                    logger.warn("Arrêt forcé du SI mock");
                    siMockProcess.destroyForcibly();
                    siMockProcess.waitFor(2, TimeUnit.SECONDS);
                }
                logger.info("SI mock arrêté");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.error("Interruption lors de l'arrêt du SI mock", e);
            }
        }

        // Nettoyer les processus orphelins si nécessaire
        try {
            logger.debug("Nettoyage des processus orphelins...");
            ProcessBuilder pb = new ProcessBuilder("pkill", "-f", "si-mock.*jar");
            pb.start().waitFor(2, TimeUnit.SECONDS);

            pb = new ProcessBuilder("pkill", "-f", "main.*jar");
            pb.start().waitFor(2, TimeUnit.SECONDS);

            // Attendre un peu pour que les ports se libèrent
            Thread.sleep(1000);
            logger.debug("Nettoyage terminé");
        } catch (Exception e) {
            logger.debug("Erreur lors du nettoyage (ignorée): {}", e.getMessage());
        }

        isRunning = false;
        logger.info("Applications arrêtées");
    }

    /**
     * Nettoie les processus orphelins avant de démarrer de nouveaux tests.
     */
    public static void nettoyer_processus_orphelins() {
        Logger logger = LoggerFactory.getLogger(TestApplicationManager.class);
        try {
            logger.debug("Nettoyage préventif des processus orphelins...");

            ProcessBuilder pb = new ProcessBuilder("pkill", "-f", "si-mock.*jar");
            Process p = pb.start();
            p.waitFor(2, TimeUnit.SECONDS);

            pb = new ProcessBuilder("pkill", "-f", "main.*jar");
            p = pb.start();
            p.waitFor(2, TimeUnit.SECONDS);

            // Attendre que les ports se libèrent
            Thread.sleep(2000);
            logger.debug("Nettoyage préventif terminé");
        } catch (Exception e) {
            logger.debug("Erreur lors du nettoyage préventif (ignorée): {}", e.getMessage());
        }
    }
}
