package fr.enedis.i2r.e2e;

import fr.enedis.i2r.e2e.infrastructure.TestApplicationManager;
import fr.enedis.i2r.e2e.infrastructure.TestHttpClient;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Test d'intégration pour vérifier que les applications peuvent être démarrées automatiquement.
 */
public class ApplicationStartupIT {

    private static final Logger logger = LoggerFactory.getLogger(ApplicationStartupIT.class);

    private TestApplicationManager applicationManager;
    private TestHttpClient httpClient;

    @BeforeEach
    void setUp() {
        applicationManager = new TestApplicationManager();
        httpClient = TestHttpClient.getInstance();
    }

    @AfterEach
    void tearDown() {
        if (applicationManager != null) {
            try {
                applicationManager.arreter_application_complete();
            } catch (Exception e) {
                logger.warn("Erreur lors de l'arrêt des applications", e);
            }
        }
    }

    @Test
    @Timeout(value = 3, unit = TimeUnit.MINUTES)
    void devrait_demarrer_les_applications_automatiquement() throws Exception {
        logger.info("=== Test de démarrage automatique des applications ===");

        // Démarrer les applications
        applicationManager.demarrer_application_complete();

        // Vérifier que les applications sont démarrées
        assertThat(applicationManager.est_application_demarree())
            .as("Les applications devraient être démarrées")
            .isTrue();

        // Vérifier que le SI mock est accessible
        assertThat(httpClient.verifier_si_mock_disponible())
            .as("Le SI mock devrait être accessible")
            .isTrue();

        // Vérifier que l'application principale est accessible
        assertThat(httpClient.verifier_application_disponible())
            .as("L'application principale devrait être accessible")
            .isTrue();

        logger.info("=== Test de démarrage automatique réussi ===");
    }
}
