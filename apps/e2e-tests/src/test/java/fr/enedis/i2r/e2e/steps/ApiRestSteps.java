package fr.enedis.i2r.e2e.steps;

import static org.assertj.core.api.Assertions.assertThat;

import java.net.http.HttpResponse;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.e2e.infrastructure.TestContext;
import fr.enedis.i2r.e2e.infrastructure.TestHttpClient;
import io.cucumber.java.fr.Alors;
import io.cucumber.java.fr.Quand;

public class ApiRestSteps {

    private static final Logger logger = LoggerFactory.getLogger(ApiRestSteps.class);

    private final TestHttpClient httpClient = TestHttpClient.getInstance();
    private final TestContext context = TestContext.getInstance();

    @Quand("j'effectue une requête GET sur {string}")
    public void j_effectue_une_requete_get_sur(String endpoint) throws Exception {
        logger.info("Requête GET sur {}", endpoint);

        try {
            HttpResponse<String> response = httpClient.effectuer_requete_get(endpoint);
            context.stocker_derniere_reponse_http(response);
            context.stocker_code_statut_http(response.statusCode());
            context.stocker_corps_reponse(response.body());

            logger.debug("Réponse reçue: status={}, body={}", response.statusCode(), response.body());
        } catch (Exception e) {
            context.stocker_derniere_exception(e);
            throw e;
        }
    }

    @Quand("j'effectue une requête POST sur {string}")
    public void j_effectue_une_requete_post_sur(String endpoint) throws Exception {
        logger.info("Requête POST sur {}", endpoint);

        try {
            HttpResponse<String> response = httpClient.effectuer_requete_post(endpoint, "", null);
            context.stocker_derniere_reponse_http(response);
            context.stocker_code_statut_http(response.statusCode());
            context.stocker_corps_reponse(response.body());

            logger.debug("Réponse reçue: status={}, body={}", response.statusCode(), response.body());
        } catch (Exception e) {
            context.stocker_derniere_exception(e);
            throw e;
        }
    }

    @Quand("j'effectue une requête PUT sur {string} avec les en-têtes et le corps:")
    public void j_effectue_une_requete_put_sur_avec_les_en_tetes_et_le_corps(String endpoint, Map<String, String> entetes, String body) throws Exception {
        logger.info("Requête PUT sur {} avec en-têtes: {} et corps: {}", endpoint, entetes, body);

        try {
            HttpResponse<String> response = httpClient.effectuer_requete_put(endpoint, body, entetes);
            context.stocker_derniere_reponse_http(response);
            context.stocker_code_statut_http(response.statusCode());
            context.stocker_corps_reponse(response.body());

            logger.debug("Réponse reçue: status={}, body={}", response.statusCode(), response.body());
        } catch (Exception e) {
            context.stocker_derniere_exception(e);
            throw e;
        }
    }

    @Quand("j'effectue une requête POST sur {string} avec le corps:")
    public void j_effectue_une_requete_post_sur_avec_le_corps(String endpoint, String body) throws Exception {
        logger.info("Requête POST sur {} avec corps: {}", endpoint, body);

        try {
            HttpResponse<String> response = httpClient.effectuer_requete_post(endpoint, body, null);
            context.stocker_derniere_reponse_http(response);
            context.stocker_code_statut_http(response.statusCode());
            context.stocker_corps_reponse(response.body());

            logger.debug("Réponse reçue: status={}, body={}", response.statusCode(), response.body());
        } catch (Exception e) {
            context.stocker_derniere_exception(e);
            throw e;
        }
    }

    @Quand("j'effectue une requête PUT sur {string} avec le corps:")
    public void j_effectue_une_requete_put_sur_avec_le_corps(String endpoint, String body) throws Exception {
        logger.info("Requête PUT sur {} avec corps: {}", endpoint, body);

        // Récupérer les en-têtes stockés depuis l'étape précédente s'ils existent
        Map<String, String> entetes = context.recuperer_entetes_http();
        logger.info("En-têtes récupérés du contexte: {}", entetes);

        try {
            HttpResponse<String> response = httpClient.effectuer_requete_put(endpoint, body, entetes);
            context.stocker_derniere_reponse_http(response);
            context.stocker_code_statut_http(response.statusCode());
            context.stocker_corps_reponse(response.body());

            logger.debug("Réponse reçue: status={}, body={}", response.statusCode(), response.body());
        } catch (Exception e) {
            context.stocker_derniere_exception(e);
            throw e;
        }
    }

    @Quand("j'effectue une requête POST sur {string} avec les paramètres:")
    public void j_effectue_une_requete_post_sur_avec_les_parametres(String endpoint, Map<String, String> parametres) throws Exception {
        logger.info("Requête POST sur {} avec paramètres: {}", endpoint, parametres);

        // Construire l'URL avec les paramètres de requête
        StringBuilder urlBuilder = new StringBuilder(endpoint);
        if (!parametres.isEmpty()) {
            urlBuilder.append("?");
            parametres.forEach((key, value) ->
                urlBuilder.append(key).append("=").append(value).append("&"));
            // Supprimer le dernier &
            urlBuilder.setLength(urlBuilder.length() - 1);
        }

        try {
            HttpResponse<String> response = httpClient.effectuer_requete_post(urlBuilder.toString(), "", null);
            context.stocker_derniere_reponse_http(response);
            context.stocker_code_statut_http(response.statusCode());
            context.stocker_corps_reponse(response.body());

            logger.debug("Réponse reçue: status={}, body={}", response.statusCode(), response.body());
        } catch (Exception e) {
            context.stocker_derniere_exception(e);
            throw e;
        }
    }

    @Quand("j'effectue une requête PUT sur {string} avec les en-têtes:")
    public void j_effectue_une_requete_put_sur_avec_les_en_tetes(String endpoint, Map<String, String> entetes) throws Exception {
        logger.info("Requête PUT sur {} avec en-têtes: {}", endpoint, entetes);

        // Stocker les en-têtes pour la prochaine étape
        context.stocker_entetes_http(entetes);
        logger.info("En-têtes stockés dans le contexte: {}", entetes);

        String body = context.recuperer_corps_reponse();
        if (body == null) {
            body = "{}";
        }

        try {
            HttpResponse<String> response = httpClient.effectuer_requete_put(endpoint, body, entetes);
            context.stocker_derniere_reponse_http(response);
            context.stocker_code_statut_http(response.statusCode());
            context.stocker_corps_reponse(response.body());

            logger.debug("Réponse reçue: status={}, body={}", response.statusCode(), response.body());
        } catch (Exception e) {
            context.stocker_derniere_exception(e);
            throw e;
        }
    }

    @Alors("le code de statut de la réponse devrait être {int}")
    public void le_code_de_statut_de_la_reponse_devrait_etre(int codeAttendu) {
        Integer codeActuel = context.recuperer_code_statut_http();
        assertThat(codeActuel)
            .as("Le code de statut de la réponse")
            .isEqualTo(codeAttendu);
    }

    @Alors("la réponse devrait contenir {string}")
    public void la_reponse_devrait_contenir(String texteAttendu) {
        String corpsReponse = context.recuperer_corps_reponse();
        assertThat(corpsReponse)
            .as("Le corps de la réponse")
            .contains(texteAttendu);
    }

    @Alors("la réponse devrait être exactement {string}")
    public void la_reponse_devrait_etre_exactement(String texteAttendu) {
        String corpsReponse = context.recuperer_corps_reponse();
        assertThat(corpsReponse)
            .as("Le corps de la réponse")
            .isEqualTo(texteAttendu);
    }

    @Alors("la réponse devrait être un JSON valide")
    public void la_reponse_devrait_etre_un_json_valide() throws Exception {
        String corpsReponse = context.recuperer_corps_reponse();
        assertThat(corpsReponse)
            .as("Le corps de la réponse ne devrait pas être null")
            .isNotNull();

        // Tenter de parser le JSON pour vérifier sa validité
        try {
            httpClient.convertir_depuis_json(corpsReponse, Object.class);
        } catch (Exception e) {
            throw new AssertionError("La réponse n'est pas un JSON valide: " + corpsReponse, e);
        }
    }

    @Alors("la réponse JSON devrait contenir le champ {string}")
    public void la_reponse_json_devrait_contenir_le_champ(String nomChamp) throws Exception {
        String corpsReponse = context.recuperer_corps_reponse();
        assertThat(corpsReponse)
            .as("Le corps de la réponse")
            .contains("\"" + nomChamp + "\"");
    }

    @Alors("la réponse JSON devrait avoir le champ {string} avec la valeur {string}")
    public void la_reponse_json_devrait_avoir_le_champ_avec_la_valeur(String nomChamp, String valeurAttendue) throws Exception {
        String corpsReponse = context.recuperer_corps_reponse();
        assertThat(corpsReponse)
            .as("Le corps de la réponse")
            .contains("\"" + nomChamp + "\":\"" + valeurAttendue + "\"");
    }

    @Alors("une erreur devrait être levée")
    public void une_erreur_devrait_etre_levee() {
        Exception exception = context.recuperer_derniere_exception();
        assertThat(exception)
            .as("Une exception devrait avoir été levée")
            .isNotNull();
    }

    @Alors("aucune erreur ne devrait être levée")
    public void aucune_erreur_ne_devrait_etre_levee() {
        Exception exception = context.recuperer_derniere_exception();
        assertThat(exception)
            .as("Aucune exception ne devrait avoir été levée")
            .isNull();
    }
}
