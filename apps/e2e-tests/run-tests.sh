#!/bin/bash

# Script pour exécuter les tests E2E de l'application I2R
# Usage: ./run-tests.sh [options]

set -e

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration par défaut
MAVEN_PROFILE=""
CUCUMBER_TAGS=""
FEATURE_FILE=""
CLEAN_BEFORE=false
SKIP_COMPILE=false
VERBOSE=false
HELP=false

# Fonction d'aide
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help              Afficher cette aide"
    echo "  -c, --clean             Nettoyer avant l'exécution"
    echo "  -s, --skip-compile      Ignorer la compilation des applications"
    echo "  -v, --verbose           Mode verbeux"
    echo "  -t, --tags TAGS         Exécuter seulement les scénarios avec ces tags"
    echo "  -f, --feature FILE      Exécuter seulement ce fichier feature"
    echo "  -p, --profile PROFILE   Utiliser ce profil Maven"
    echo ""
    echo "Exemples:"
    echo "  $0                                    # Exécuter tous les tests"
    echo "  $0 -c                                # Nettoyer et exécuter tous les tests"
    echo "  $0 -t @smoke                        # Exécuter seulement les tests smoke"
    echo "  $0 -f api_rest.feature              # Exécuter seulement les tests API REST"
    echo "  $0 -t '@smoke or @regression'       # Exécuter les tests smoke et regression"
    echo "  $0 -s -v                            # Mode verbeux sans compilation"
}

# Fonction de log
log() {
    local level=$1
    shift
    local message="$@"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${GREEN}[INFO]${NC} ${timestamp} - $message"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} ${timestamp} - $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} ${timestamp} - $message"
            ;;
        "DEBUG")
            if [ "$VERBOSE" = true ]; then
                echo -e "${BLUE}[DEBUG]${NC} ${timestamp} - $message"
            fi
            ;;
    esac
}

# Analyser les arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            HELP=true
            shift
            ;;
        -c|--clean)
            CLEAN_BEFORE=true
            shift
            ;;
        -s|--skip-compile)
            SKIP_COMPILE=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -t|--tags)
            CUCUMBER_TAGS="$2"
            shift 2
            ;;
        -f|--feature)
            FEATURE_FILE="$2"
            shift 2
            ;;
        -p|--profile)
            MAVEN_PROFILE="-P$2"
            shift 2
            ;;
        *)
            log "ERROR" "Option inconnue: $1"
            show_help
            exit 1
            ;;
    esac
done

# Afficher l'aide si demandé
if [ "$HELP" = true ]; then
    show_help
    exit 0
fi

# Vérifier que nous sommes dans le bon répertoire
if [ ! -f "pom.xml" ]; then
    log "ERROR" "Ce script doit être exécuté depuis le répertoire apps/e2e-tests/"
    exit 1
fi

# Aller à la racine du projet
cd ../..

log "INFO" "Démarrage des tests E2E I2R"

# Nettoyage si demandé
if [ "$CLEAN_BEFORE" = true ]; then
    log "INFO" "Nettoyage des artefacts précédents..."
    mvn clean -pl apps/e2e-tests
    rm -rf apps/e2e-tests/target/test-data/
fi

# Compilation des applications si nécessaire
if [ "$SKIP_COMPILE" = false ]; then
    log "INFO" "Compilation des applications..."
    
    # Vérifier si les JARs existent déjà
    MAIN_JAR="apps/main/target/main-1.0.1-SNAPSHOT.jar"
    SI_MOCK_JAR="apps/si-mock/target/si-mock-1.0.1-SNAPSHOT.jar"
    
    if [ ! -f "$MAIN_JAR" ] || [ ! -f "$SI_MOCK_JAR" ]; then
        log "INFO" "Compilation des modules requis..."
        mvn clean package -pl apps/main,apps/si-mock -DskipTests $MAVEN_PROFILE
    else
        log "DEBUG" "Les JARs existent déjà, compilation ignorée"
    fi
else
    log "INFO" "Compilation ignorée (--skip-compile)"
fi

# Construire la commande Maven
MAVEN_CMD="mvn verify -pl apps/e2e-tests $MAVEN_PROFILE"

# Ajouter les options Cucumber si spécifiées
if [ -n "$CUCUMBER_TAGS" ]; then
    log "INFO" "Exécution avec les tags: $CUCUMBER_TAGS"
    MAVEN_CMD="$MAVEN_CMD -Dcucumber.filter.tags=\"$CUCUMBER_TAGS\""
fi

if [ -n "$FEATURE_FILE" ]; then
    log "INFO" "Exécution du fichier feature: $FEATURE_FILE"
    MAVEN_CMD="$MAVEN_CMD -Dcucumber.features=\"src/test/resources/features/$FEATURE_FILE\""
fi

# Ajouter le mode verbeux si demandé
if [ "$VERBOSE" = true ]; then
    MAVEN_CMD="$MAVEN_CMD -X"
fi

# Exécuter les tests
log "INFO" "Exécution des tests E2E..."
log "DEBUG" "Commande Maven: $MAVEN_CMD"

# Exécuter la commande
eval $MAVEN_CMD

# Vérifier le résultat
if [ $? -eq 0 ]; then
    log "INFO" "Tests E2E terminés avec succès"
    
    # Afficher les liens vers les rapports
    echo ""
    log "INFO" "Rapports disponibles:"
    echo "  - HTML: apps/e2e-tests/target/cucumber-reports/index.html"
    echo "  - JSON: apps/e2e-tests/target/cucumber-reports/Cucumber.json"
    echo "  - JUnit: apps/e2e-tests/target/cucumber-reports/Cucumber.xml"
    echo "  - Failsafe: apps/e2e-tests/target/failsafe-reports/"
else
    log "ERROR" "Les tests E2E ont échoué ❌"
    echo ""
    log "INFO" "Consultez les logs pour plus de détails:"
    echo "  - Logs Maven: Sortie ci-dessus"
    echo "  - Rapports: apps/e2e-tests/target/failsafe-reports/"
    echo "  - Logs des applications: Vérifiez les processus Java"
    exit 1
fi
