<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>fr.enedis.i2r</groupId>
        <artifactId>apps</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>e2e-tests</artifactId>
    <packaging>jar</packaging>
    <name>I2R E2E Tests</name>
    <description>Tests d'intégration end-to-end avec Cucumber pour l'application I2R</description>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <maven.install.skip>true</maven.install.skip>
        <!-- Default: E2E tests are enabled for local development -->
        <skipE2ETests>false</skipE2ETests>
        <e2e.dependency.phase>pre-integration-test</e2e.dependency.phase>
    </properties>

    <profiles>
        <!-- Profile to skip E2E tests in CI environment -->
        <profile>
            <id>skip-e2e-ci</id>
            <activation>
                <property>
                    <name>env.CI</name>
                    <value>true</value>
                </property>
            </activation>
            <properties>
                <skipE2ETests>true</skipE2ETests>
                <e2e.dependency.phase>none</e2e.dependency.phase>
            </properties>
        </profile>

        <!-- Profile to manually skip E2E tests -->
        <profile>
            <id>skip-e2e</id>
            <properties>
                <skipE2ETests>true</skipE2ETests>
                <e2e.dependency.phase>none</e2e.dependency.phase>
            </properties>
        </profile>
    </profiles>

    <dependencies>
        <!-- Test dependencies are inherited from parent POM -->

        <!-- HTTP Client for API testing -->
        <dependency>
            <groupId>io.javalin</groupId>
            <artifactId>javalin</artifactId>
            <version>${javalin.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- JSON processing -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.18.1</version>
            <scope>test</scope>
        </dependency>


    </dependencies>

    <build>
        <plugins>
            <!-- Disable Code Coverage for E2E tests module -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>none</phase>
                        <id>coverage-initialize</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <inherited>false</inherited>
                    </execution>
                    <execution>
                        <id>coverage-report</id>
                        <phase>none</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <inherited>false</inherited>
                    </execution>
                </executions>
            </plugin>

            <!-- Maven Failsafe Plugin for Integration Tests -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <configuration>
                    <!-- Skip E2E tests in CI environment, enabled by default for local development -->
                    <skipTests>${skipE2ETests}</skipTests>
                    <skipITs>${skipE2ETests}</skipITs>
                    <includes>
                        <include>**/*IT.java</include>
                        <include>**/CucumberTestRunner.java</include>
                    </includes>
                    <systemPropertyVariables>
                        <cucumber.junit-platform.naming-strategy>long</cucumber.junit-platform.naming-strategy>
                    </systemPropertyVariables>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>integration-test</goal>
                            <goal>verify</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Maven Surefire Plugin - Skip unit tests in this module -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>

            <!-- Build dependencies before running E2E tests -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>build-dependencies</id>
                        <!-- Skip dependency building when E2E tests are skipped -->
                        <phase>${e2e.dependency.phase}</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <skip>${skipE2ETests}</skip>
                            <executable>${project.basedir}/../../mvnw</executable>
                            <workingDirectory>${project.basedir}/../..</workingDirectory>
                            <arguments>
                                <argument>package</argument>
                                <argument>-pl</argument>
                                <argument>apps/main,apps/si-mock</argument>
                                <argument>-am</argument>
                                <argument>-DskipTests</argument>
                            </arguments>
                            <outputFile>${project.build.directory}/build.log</outputFile>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
