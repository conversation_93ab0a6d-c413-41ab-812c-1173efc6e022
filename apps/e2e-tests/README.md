# Tests E2E pour l'application I2R

Ce module contient les tests end-to-end (E2E) pour l'application I2R utilisant Cucumber et JUnit Platform.

## **Démarrage automatique des applications**

Les tests E2E démarrent automatiquement toutes les applications requises (SI mock) sans configuration manuelle. Il suffit d'exécuter une seule commande pour lancer l'ensemble des 44 tests E2E.

## Structure du projet

```
apps/e2e-tests/
├── src/test/java/fr/enedis/i2r/e2e/
│   ├── CucumberTestRunner.java          # Test runner principal
│   ├── configuration/
│   │   └── TestConfiguration.java       # Configuration centralisée
│   ├── hooks/
│   │   └── TestHooks.java              # Hooks Cucumber (setup/teardown)
│   ├── infrastructure/
│   │   ├── TestApplicationManager.java  # Gestion du cycle de vie des applications
│   │   ├── TestContext.java            # Contexte partagé entre les steps
│   │   └── TestHttpClient.java         # Client HTTP pour les tests
│   └── steps/
│       ├── ApplicationSteps.java       # Steps pour la gestion de l'application
│       ├── ApiRestSteps.java          # Steps pour les APIs REST
│       ├── BoitierSteps.java          # Steps pour la gestion du boîtier
│       └── CommunicationSiSteps.java  # Steps pour la communication SI
└── src/test/resources/
    ├── features/                       # Fichiers .feature en français
    │   ├── gestion_application.feature
    │   ├── gestion_etats_boitier.feature
    │   ├── communication_si.feature
    │   ├── api_rest.feature
    │   └── surveillance_watchdog.feature
    ├── cucumber.properties             # Configuration Cucumber
    └── junit-platform.properties      # Configuration JUnit Platform
```

## Prérequis

1. **Java 21** ou supérieur
2. **Maven Wrapper** (inclus dans le projet)

## **Exécution automatisée des tests**

### ** Commandes Mask (recommandées)**

```bash
# Lance les tests E2E
mask test e2e
```

### ** Commande Maven alternative**

```bash
# Depuis la racine du projet - Lance automatiquement tous les tests E2E
# Les applications requises (SI mock) démarrent automatiquement
./mvnw verify -pl apps/e2e-tests
```

**Cette commande unique** :
- Compile automatiquement les dépendances (SI mock)
- Démarre automatiquement le SI mock
- Exécute les tests E2E
- Nettoie automatiquement après les tests
- Aucune configuration manuelle requise

### Autres commandes utiles

```bash
# Tests unitaires seulement (sans démarrage d'applications)
./mvnw test -pl apps/e2e-tests

# Tests avec tags spécifiques
./mvnw verify -pl apps/e2e-tests -Dcucumber.filter.tags="@api_rest"
```

### Ordre recommandé pour les tests

```bash
# 1. Tests unitaires de tous les modules
./mvnw test

# 2. Tests E2E automatisés (avec démarrage automatique des apps)
./mvnw verify -pl apps/e2e-tests

# 3. Tests complets du projet (optionnel)
./mvnw verify
```

## Configuration

### Variables d'environnement

Les tests peuvent être configurés via des variables d'environnement :

| Variable                | Description                          | Valeur par défaut                    |
| ----------------------- | ------------------------------------ | ------------------------------------ |
| `I2R_APP_URL`           | URL de l'application I2R             | `http://localhost:8081`              |
| `I2R_SI_MOCK_URL`       | URL du SI mock                       | `http://localhost:8440`              |
| `I2R_TEST_DB_PATH`      | Chemin de la base de données de test | `target/test-data/i2r-params-e2e.db` |
| `I2R_TEST_METRICS_PATH` | Chemin des métriques de test         | `target/test-data/metrics`           |
| `I2R_TEST_IDMS`         | IDMS de test                         | `12345678901234567890123456789012`   |
| `I2R_TEST_CONFIG_HASH`  | Hash de configuration de test        | `abc123def456`                       |
| `I2R_LOG_LEVEL`         | Niveau de log                        | `INFO`                               |

### Fichier de configuration

Créez un fichier `src/test/resources/test-config.properties` pour personnaliser la configuration :

```properties
# URLs des services
app.base.url=http://localhost:8081
si.mock.url=http://localhost:8440

# Timeouts (en secondes)
app.startup.timeout=60
si.startup.timeout=30
http.request.timeout=10

# Données de test
test.idms=12345678901234567890123456789012
test.config.hash=abc123def456
test.api.version=1.0

# Configuration des logs
log.level=INFO
log.test.details=true
```

## Rapports

Les rapports de tests sont générés dans :

**HTML** : `\\wsl.localhost\Ubuntu-24.04\home\%USERNAME%\projects\i2r\apps\e2e-tests\target\cucumber-reports\index.html`
1. Remplacer `%USERNAME%` par votre nom d'utilisateur WSL
2. Copiez le chemin affiché dans votre navigateur Windows

## Conventions de nommage

### Step definitions

Toutes les méthodes de step definitions suivent la convention **snake_case en français** :

```java
@Etantdonné("que l'application I2R est démarrée")
public void que_l_application_i2r_est_demarree() { }

@Quand("je change l'état du boîtier vers {string}")
public void je_change_l_etat_du_boitier_vers(String nouvelEtat) { }

@Alors("le code de statut de la réponse devrait être {int}")
public void le_code_de_statut_de_la_reponse_devrait_etre(int codeAttendu) { }
```

### Fichiers feature

Les fichiers `.feature` sont écrits en français avec la directive `# language: fr` :

```gherkin
# language: fr
Fonctionnalité: Gestion de l'application I2R

  Scénario: Démarrage réussi de l'application
    Étant donné que l'application I2R est arrêtée
    Quand je démarre l'application I2R
    Alors l'application I2R devrait être en cours d'exécution
```

## Dépannage

### Problèmes courants

1. **Applications non compilées** :
   ```bash
   # Normalement pas nécessaire car la compilation est automatique
   ./mvnw package -pl apps/si-mock -am -DskipTests
   ```

2. **Ports déjà utilisés** :
   - Vérifiez que les ports 8440 et 8443 sont libres (SI mock)
   - Le nettoyage automatique tue les processus orphelins
   - Modifiez les URLs dans la configuration si nécessaire

3. **Timeouts** :
   - Augmentez les timeouts dans la configuration
   - Vérifiez les logs des applications dans `target/test-data/`

4. **Base de données de test** :
   - Le répertoire `target/test-data/` est créé automatiquement
   - Supprimez-le pour un nettoyage complet

### Logs de debug

Pour activer les logs détaillés :

```bash
./mvnw verify -pl apps/e2e-tests -DI2R_LOG_LEVEL=DEBUG
```

### Nettoyage

```bash
# Nettoyer les données de test
rm -rf apps/e2e-tests/target/test-data/

# Nettoyer complètement
./mvnw clean -pl apps/e2e-tests
```

## Développement

### Ajouter de nouveaux tests

1. **Créer un nouveau fichier .feature** dans `src/test/resources/features/`
2. **Implémenter les step definitions** dans `src/test/java/fr/enedis/i2r/e2e/steps/`
3. **Utiliser les classes d'infrastructure** existantes (`TestContext`, `TestHttpClient`, etc.)

### Bonnes pratiques

- Utilisez `TestContext` pour partager des données entre les steps
- Préférez les méthodes utilitaires de `TestHttpClient` pour les requêtes HTTP
- Respectez la convention de nommage snake_case en français
- Ajoutez des logs informatifs avec le logger SLF4J
- Utilisez AssertJ pour les assertions

## Support

Pour toute question ou problème, consultez :

- La documentation du projet I2R
- Les logs des tests dans `target/surefire-reports/`
- Les rapports Cucumber dans `target/cucumber-reports/`
