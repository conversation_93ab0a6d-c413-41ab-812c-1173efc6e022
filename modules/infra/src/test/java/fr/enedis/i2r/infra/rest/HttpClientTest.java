package fr.enedis.i2r.infra.rest;

import static java.util.Collections.emptyMap;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import java.net.URI;
import java.time.Duration;

import javax.net.ssl.SSLContext;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import fr.enedis.i2r.infra.rest.si.error.IComBadRequestException;
import io.javalin.http.HttpStatus;

class HttpClientTest {

    HttpClient client;
    HttpClient spyClient;

    HttpRequest request;

    @BeforeEach
    void setup() throws Exception {
        request = new HttpRequest(
            "GET",
            new URI("https://127.0.0.1:8443/ping"),
            emptyMap(),
            "",
            Encoding.NONE);

        client = new HttpClient(SSLContext.getDefault());
        spyClient = Mockito.spy(client);
    }

    @Test
    void la_requete_reussie_n_effectue_qu_un_seul_appel() throws IComBadRequestException {
        HttpResponse ret = new HttpResponse();
        ret.setStatus(HttpStatus.OK);

        doReturn(ret).when(spyClient).sendRequest(any());

        var result = spyClient.retryRequest(request);

        assertEquals(200, result.getStatus().getCode());
        verify(spyClient, times(1)).sendRequest(any());
    }

    @Test
    void la_requete_echouee_effectue_trois_tentatives() throws IComBadRequestException {
        Mockito.doThrow(new IComBadRequestException(404, "erreur")).when(spyClient).sendRequest(any());

        assertThatExceptionOfType(IComBadRequestException.class).isThrownBy(
            () -> spyClient.retryRequest(request, new RetryConfig(3, Duration.ZERO))
        );

        verify(spyClient, times(3)).sendRequest(any());
    }

    @Test
    void l_interruption_pendant_le_delai_est_geree() throws IComBadRequestException {
        Thread.currentThread().interrupt();

        Mockito.doThrow(new IComBadRequestException(500, "erreur")).when(spyClient).sendRequest(any());

        assertThatExceptionOfType(IComBadRequestException.class).isThrownBy(
            () -> spyClient.retryRequest(request, new RetryConfig(2, Duration.ofSeconds(1)))
        );

        verify(spyClient, times(2)).sendRequest(any());
    }


}
