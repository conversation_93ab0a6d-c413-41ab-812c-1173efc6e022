package fr.enedis.i2r.infra.metrics;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.Test;

import fr.enedis.i2r.comsi.metrics.ServingCellMetrics;

class MetricsParserTest {

    @Test
    void les_metriques_serving_cell_valides_sont_parsees_correctement() {
        Map<String, String> fields = createValidServingCellFields();
        RawJsonMetric rawMetric = new RawJsonMetric(fields, "serving-cell", Map.of(), 1756296906L);

        ServingCellMetrics result = MetricsParser.parseServingCell(rawMetric);

        assertThat(result.earfcn()).isEqualTo(1300);
        assertThat(result.mcc()).isEqualTo(208);
        assertThat(result.mnc()).isEqualTo(1);
        assertThat(result.pci()).isEqualTo(334);
        assertThat(result.techno()).isEqualTo("eMTC");
        assertThat(result.rsrp()).isEqualTo(-84);
        assertThat(result.rsrq()).isEqualTo(-3);
        assertThat(result.rssi()).isEqualTo(-66);
        assertThat(result.sinr()).isEqualTo(-17);
        assertThat(result.tac()).isEqualTo(51908);

        ZonedDateTime expectedTime = Instant.ofEpochSecond(1756296906L).atZone(ZoneId.systemDefault());
        assertThat(result.emittedTime()).isEqualTo(expectedTime);
    }

    @Test
    void les_differents_types_de_technologie_sont_geres() {
        Map<String, String> fields = createValidServingCellFields();
        fields.put("rat", "LTE");
        RawJsonMetric rawMetric = new RawJsonMetric(fields, "serving-cell", Map.of(), 1756296906L);

        ServingCellMetrics result = MetricsParser.parseServingCell(rawMetric);

        assertThat(result.techno()).isEqualTo("LTE");
    }

    @Test
    void les_valeurs_de_signal_negatives_sont_gerees() {
        Map<String, String> fields = createValidServingCellFields();
        fields.put("rsrp", "-95");
        fields.put("rsrq", "-10");
        fields.put("rssi", "-70");
        fields.put("sinr", "-20");
        RawJsonMetric rawMetric = new RawJsonMetric(fields, "serving-cell", Map.of(), 1756296906L);

        ServingCellMetrics result = MetricsParser.parseServingCell(rawMetric);

        assertThat(result.rsrp()).isEqualTo(-95);
        assertThat(result.rsrq()).isEqualTo(-10);
        assertThat(result.rssi()).isEqualTo(-70);
        assertThat(result.sinr()).isEqualTo(-20);
    }

    @Test
    void les_champs_manquants_levent_une_exception() {
        Map<String, String> fields = createValidServingCellFields();
        fields.remove("earfcn");
        RawJsonMetric rawMetric = new RawJsonMetric(fields, "serving-cell", Map.of(), 1756296906L);

        assertThatThrownBy(() -> MetricsParser.parseServingCell(rawMetric))
            .isInstanceOf(NumberFormatException.class);
    }

    @Test
    void les_valeurs_invalides_levent_une_exception() {
        Map<String, String> fields = createValidServingCellFields();
        fields.put("mcc", "invalid");
        RawJsonMetric rawMetric = new RawJsonMetric(fields, "serving-cell", Map.of(), 1756296906L);

        assertThatThrownBy(() -> MetricsParser.parseServingCell(rawMetric))
            .isInstanceOf(NumberFormatException.class);
    }

    @Test
    void les_valeurs_limites_sont_gerees_correctement() {
        Map<String, String> fields = new HashMap<>();
        fields.put("earfcn", "0");
        fields.put("mcc", "999");
        fields.put("mnc", "999");
        fields.put("pci", "503");
        fields.put("rat", "NB-IOT");
        fields.put("rsrp", "-140");
        fields.put("rsrq", "-20");
        fields.put("rssi", "-110");
        fields.put("sinr", "-20");
        fields.put("tac", "65535");

        RawJsonMetric rawMetric = new RawJsonMetric(fields, "serving-cell", Map.of(), 1756296906L);

        ServingCellMetrics result = MetricsParser.parseServingCell(rawMetric);

        assertThat(result.earfcn()).isEqualTo(0);
        assertThat(result.mcc()).isEqualTo(999);
        assertThat(result.mnc()).isEqualTo(999);
        assertThat(result.pci()).isEqualTo(503);
        assertThat(result.techno()).isEqualTo("NB-IOT");
        assertThat(result.rsrp()).isEqualTo(-140);
        assertThat(result.rsrq()).isEqualTo(-20);
        assertThat(result.rssi()).isEqualTo(-110);
        assertThat(result.sinr()).isEqualTo(-20);
        assertThat(result.tac()).isEqualTo(65535);
    }

    private Map<String, String> createValidServingCellFields() {
        Map<String, String> fields = new HashMap<>();
        fields.put("earfcn", "1300");
        fields.put("mcc", "208");
        fields.put("mnc", "1");
        fields.put("pci", "334");
        fields.put("rat", "eMTC");
        fields.put("rsrp", "-84");
        fields.put("rsrq", "-3");
        fields.put("rssi", "-66");
        fields.put("sinr", "-17");
        fields.put("tac", "51908");
        return fields;
    }
}
