package fr.enedis.i2r.infra.systemd;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import org.junit.jupiter.api.Test;

class UnixSocketTest {

    @Test
    void la_creation_avec_chemin_absolu_reussit() throws InvalidSocketTypeException {
        UnixSocket socket = new UnixSocket("/tmp/test.sock");

        assertThat(socket).isNotNull();
    }

    @Test
    void la_creation_avec_abstract_namespace_reussit() throws InvalidSocketTypeException {
        UnixSocket socket = new UnixSocket("@test-socket");

        assertThat(socket).isNotNull();
    }

    @Test
    void la_creation_avec_chemin_relatif_leve_une_exception() {
        assertThatThrownBy(() -> new UnixSocket("relative/path"))
            .isInstanceOf(InvalidSocketTypeException.class);
    }

    @Test
    void la_creation_avec_chemin_vide_leve_une_exception() {
        assertThatThrownBy(() -> new UnixSocket(""))
            .isInstanceOf(InvalidSocketTypeException.class);
    }

    @Test
    void la_creation_avec_chemin_invalide_leve_une_exception() {
        assertThatThrownBy(() -> new UnixSocket("invalid-path"))
            .isInstanceOf(InvalidSocketTypeException.class);
    }

    @Test
    void la_notification_avec_chemin_absolu_ne_leve_pas_d_exception() throws InvalidSocketTypeException {
        UnixSocket socket = new UnixSocket("/tmp/nonexistent.sock");

        socket.notify("READY=1");
    }

    @Test
    void la_notification_avec_abstract_namespace_ne_leve_pas_d_exception() throws InvalidSocketTypeException {
        UnixSocket socket = new UnixSocket("@test-notify");

        socket.notify("WATCHDOG=1");
    }

    @Test
    void la_notification_avec_message_vide_ne_leve_pas_d_exception() throws InvalidSocketTypeException {
        UnixSocket socket = new UnixSocket("/tmp/test.sock");

        socket.notify("");
    }

    @Test
    void la_notification_avec_message_complexe_ne_leve_pas_d_exception() throws InvalidSocketTypeException {
        UnixSocket socket = new UnixSocket("/tmp/test.sock");

        socket.notify("READY=1\nSTATUS=Service started\nMAINPID=1234");
    }

    @Test
    void les_chemins_avec_arobase_sont_transformes_correctement() throws InvalidSocketTypeException {
        UnixSocket socket1 = new UnixSocket("@systemd-notify");
        UnixSocket socket2 = new UnixSocket("@/tmp/abstract");

        assertThat(socket1).isNotNull();
        assertThat(socket2).isNotNull();
    }

    @Test
    void les_chemins_absolus_restent_inchanges() throws InvalidSocketTypeException {
        UnixSocket socket1 = new UnixSocket("/run/systemd/notify");
        UnixSocket socket2 = new UnixSocket("/tmp/socket.sock");

        assertThat(socket1).isNotNull();
        assertThat(socket2).isNotNull();
    }
}
