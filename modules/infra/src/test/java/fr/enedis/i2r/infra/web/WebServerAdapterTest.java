package fr.enedis.i2r.infra.web;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import fr.enedis.i2r.comsi.ComSiConfiguration;
import fr.enedis.i2r.comsi.ports.BoardManagerPort;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.comsi.ports.ModemManagerPort;
import fr.enedis.i2r.comsi.ports.SecurityModulePort;
import fr.enedis.i2r.comsi.ports.si.SiClientPort;
import fr.enedis.i2r.comsi.status.BipStatusManager;
import fr.enedis.i2r.system.LoggingLoader;
import fr.enedis.i2r.system.ports.ShellExecutorPort;
import fr.enedis.i2r.system.watchdog.ThreadWatchdog;
import io.javalin.community.ssl.SslPlugin;

class WebServerAdapterTest {

    @Mock
    private BipStatusManager bipStatusManager;
    @Mock
    private ComSiConfiguration comSiConfiguration;
    @Mock
    private ShellExecutorPort shellExecutorPort;
    @Mock
    private LoggingLoader loggingLoader;
    @Mock
    private SslPlugin sslPlugin;
    @Mock
    private ComsiParametersPort parametersPort;
    @Mock
    private BoardManagerPort boardManagerPort;
    @Mock
    private SecurityModulePort moduleSecuritePort;
    @Mock
    private ModemManagerPort modemManagerPort;
    @Mock
    private SiClientPort siClientPort;
    @Mock
    private ThreadWatchdog threadWatchdog;
    @Mock
    private JavalinHttpServer javalinServer;

    private WebServerAdapter webServerAdapter;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        webServerAdapter = new WebServerAdapter(
            bipStatusManager,
            comSiConfiguration,
            shellExecutorPort,
            loggingLoader,
            sslPlugin,
            parametersPort,
            boardManagerPort,
            moduleSecuritePort,
            modemManagerPort,
            siClientPort,
            threadWatchdog
        );
    }

    @Test
    void le_serveur_web_demarre_et_s_arrete_correctement() {
        assertThat(webServerAdapter).isNotNull();
        assertThat(webServerAdapter.isRunning()).isFalse();

        webServerAdapter.start();
        assertThat(webServerAdapter.isRunning()).isTrue();

        webServerAdapter.stop();
        assertThat(webServerAdapter.isRunning()).isFalse();

        webServerAdapter.run();
        assertThat(webServerAdapter.isRunning()).isTrue();
    }
}
