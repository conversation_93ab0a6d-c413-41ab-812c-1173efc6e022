package fr.enedis.i2r.infra.rest.si;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.Map;

import org.junit.jupiter.api.Test;

class SiEndpointTest {

    @Test
    void l_endpoint_send_config_a_le_bon_template_et_methode() {
        assertThat(SiEndpoint.SendConfig.endpointTemplate).isEqualTo("/SIService/{idms}/db/cfg");
        assertThat(SiEndpoint.SendConfig.httpMethod).isEqualTo("PUT");
    }

    @Test
    void l_endpoint_send_metrics_a_le_bon_template_et_methode() {
        assertThat(SiEndpoint.SendMetrics.endpointTemplate).isEqualTo("/SIService/{idms}/db/stats");
        assertThat(SiEndpoint.SendMetrics.httpMethod).isEqualTo("PUT");
    }

    @Test
    void la_generation_de_chemin_avec_un_parametre_fonctionne() {
        Map<String, String> pathParams = Map.of("idms", "test-idms");

        String path = SiEndpoint.SendConfig.getEndpointPath(pathParams);

        assertThat(path).isEqualTo("/SIService/test-idms/db/cfg");
    }

    @Test
    void la_generation_de_chemin_remplace_correctement_les_parametres() {
        Map<String, String> pathParams = Map.of("idms", "my-device-123");

        String configPath = SiEndpoint.SendConfig.getEndpointPath(pathParams);
        String metricsPath = SiEndpoint.SendMetrics.getEndpointPath(pathParams);

        assertThat(configPath).isEqualTo("/SIService/my-device-123/db/cfg");
        assertThat(metricsPath).isEqualTo("/SIService/my-device-123/db/stats");
    }

    @Test
    void la_generation_de_chemin_sans_parametres_retourne_le_template_original() {
        Map<String, String> emptyParams = Map.of();

        String path = SiEndpoint.SendConfig.getEndpointPath(emptyParams);

        assertThat(path).isEqualTo("/SIService/{idms}/db/cfg");
    }

    @Test
    void la_generation_de_chemin_avec_parametres_inexistants_ne_modifie_pas_le_template() {
        Map<String, String> wrongParams = Map.of("wrong", "value");

        String path = SiEndpoint.SendConfig.getEndpointPath(wrongParams);

        assertThat(path).isEqualTo("/SIService/{idms}/db/cfg");
    }

    @Test
    void la_generation_de_chemin_avec_valeurs_speciales_fonctionne() {
        Map<String, String> specialParams = Map.of("idms", "<EMAIL>");

        String path = SiEndpoint.SendMetrics.getEndpointPath(specialParams);

        assertThat(path).isEqualTo("/SIService/<EMAIL>/db/stats");
    }

    @Test
    void la_generation_de_chemin_avec_valeurs_vides_fonctionne() {
        Map<String, String> emptyValueParams = Map.of("idms", "");

        String path = SiEndpoint.SendConfig.getEndpointPath(emptyValueParams);

        assertThat(path).isEqualTo("/SIService//db/cfg");
    }
}
