package fr.enedis.i2r.infra.params;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.io.IOException;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.WatchService;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;

import fr.enedis.i2r.comsi.params.BipParameter;
import fr.enedis.i2r.comsi.params.ConfigurationValue;

class DbUpdateWatcherAdapterTest {

    private Path tempDir;
    private Path watchedFile;
    private WatchService watchService;
    private DbUpdateWatcherAdapter watcher;
    private ExecutorService executor;
    private SqliteParamsProvider paramsProvider;

    @BeforeEach
    void setup() throws IOException, SQLException {
        tempDir = Files.createTempDirectory("watch_test");
        watchedFile = tempDir.resolve("params.db");
        Files.createFile(watchedFile);

        paramsProvider = new SqliteParamsProvider(watchedFile.toString());

        watchService = FileSystems.getDefault().newWatchService();
        watcher = new DbUpdateWatcherAdapter(paramsProvider, watchedFile.toString());

        executor = Executors.newSingleThreadExecutor();
    }

    @AfterEach
    void teardown() throws IOException {
        executor.shutdownNow();
        watchService.close();
        Files.deleteIfExists(watchedFile);
        Files.deleteIfExists(tempDir);
    }

    @Test
    void les_nouveaux_parametres_sont_bien_detectes() throws SQLException, InterruptedException {
        var modifiedParams = paramsProvider.fetchAndResetUpdatedParameters();

        assertThat(modifiedParams).isEmpty();

        var connection = DriverManager.getConnection("jdbc:sqlite:" + watchedFile.toString());
        var sql = "INSERT INTO parameters(param_name, param_value) VALUES(?, ?)";
        var upsertStatement = connection.prepareStatement(sql);

        upsertStatement.setString(1, BipParameter.BipState.parameterKey);
        upsertStatement.setString(2, "2");

        upsertStatement.executeUpdate();

        modifiedParams = paramsProvider.fetchAndResetUpdatedParameters();
        assertThat(modifiedParams).hasSize(1);
        assertThat(modifiedParams.get(BipParameter.BipState.parameterKey)).isEqualTo("2");
    }

    @Test
    void les_mises_a_jour_ne_sont_recuperees_qu_une_seule_fois() throws SQLException {
        var connection = DriverManager.getConnection("jdbc:sqlite:" + watchedFile.toString());
        var sql = "INSERT INTO parameters(param_name, param_value) VALUES(?, ?)";
        var upsertStatement = connection.prepareStatement(sql);

        upsertStatement.setString(1, BipParameter.BipState.parameterKey);
        upsertStatement.setString(2, "2");
        upsertStatement.executeUpdate();

        var modifiedParams = paramsProvider.fetchAndResetUpdatedParameters();
        assertThat(modifiedParams).hasSize(1);

        modifiedParams = paramsProvider.fetchAndResetUpdatedParameters();
        assertThat(modifiedParams).isEmpty();
    }

    @Test
    void les_parametres_mis_a_jour_sont_detectes() throws SQLException, InterruptedException {
        var connection = DriverManager.getConnection("jdbc:sqlite:" + watchedFile.toString());
        var sql = "INSERT INTO parameters(param_name, param_value) VALUES(?, ?)";
        var upsertStatement = connection.prepareStatement(sql);

        upsertStatement.setString(1, BipParameter.BipState.parameterKey);
        upsertStatement.setString(2, "2");

        upsertStatement.executeUpdate();

        var modifiedParams = paramsProvider.fetchAndResetUpdatedParameters();
        assertThat(modifiedParams).hasSize(1);

        modifiedParams = paramsProvider.fetchAndResetUpdatedParameters();
        assertThat(modifiedParams).isEmpty();

        sql = "INSERT INTO parameters(param_name, param_value) VALUES(?, ?) ON CONFLICT(param_name) DO UPDATE SET param_value=excluded.param_value";
        upsertStatement = connection.prepareStatement(sql);

        upsertStatement.setString(1, BipParameter.BipState.parameterKey);
        upsertStatement.setString(2, "3");

        upsertStatement.executeUpdate();

        modifiedParams = paramsProvider.fetchAndResetUpdatedParameters();
        assertThat(modifiedParams).hasSize(1);
        assertThat(modifiedParams.get(BipParameter.BipState.parameterKey)).isEqualTo("3");
    }

    @Test
    void les_parametres_inconnus_ne_sont_pas_recuperes() throws SQLException, InterruptedException {
        var connection = DriverManager.getConnection("jdbc:sqlite:" + watchedFile.toString());
        var sql = "INSERT INTO parameters(param_name, param_value) VALUES(?, ?)";
        var upsertStatement = connection.prepareStatement(sql);

        upsertStatement.setString(1, "parametre inconnu");
        upsertStatement.setString(2, "2");

        upsertStatement.executeUpdate();

        var updatedValues = watcher.fetchUpdatedValues();

        assertThat(updatedValues).isEmpty();
    }

    @Test
    @Timeout(value = 2, unit = TimeUnit.SECONDS)
    void les_parametres_mis_a_jour_sont_reconnus_par_comsi() throws SQLException, InterruptedException, ExecutionException {
        var connection = DriverManager.getConnection("jdbc:sqlite:" + watchedFile.toString());
        var sql = "INSERT INTO parameters(param_name, param_value) VALUES(?, ?)";
        var upsertStatement = connection.prepareStatement(sql);

        upsertStatement.setString(1, BipParameter.BipState.parameterKey);
        upsertStatement.setString(2, "2");

        upsertStatement.executeUpdate();

        var configurationValues = watcher.fetchUpdatedValues();
        assertThat(configurationValues).hasSize(1);
        assertThat(configurationValues.get(0).parameter().parameterKey).isEqualTo(BipParameter.BipState.parameterKey);
        assertThat(configurationValues.get(0).value()).isEqualTo("2");
    }


    @Test
    void map_correctement_les_parametres_connus() throws Exception {
        SqliteParamsProvider mockParamsProvider = mock(SqliteParamsProvider.class);
        HashMap<String, String> params = new HashMap<>();
        params.put("i2r.bip.status", "2");

        when(mockParamsProvider.fetchAndResetUpdatedParameters()).thenReturn(params);

        DbUpdateWatcherAdapter adapter = new DbUpdateWatcherAdapter(mockParamsProvider, watchedFile.toString());

        List<ConfigurationValue> result = adapter.mapParametersToConfigurationValues(params);

        assertThat(result).hasSize(1);
        ConfigurationValue configValue = result.get(0);
        assertThat(configValue.parameter()).isEqualTo(BipParameter.BipState);
        assertThat(configValue.value()).isEqualTo("2");
    }

    @Test
    void ignore_les_parametres_inconnus() throws Exception {
        SqliteParamsProvider mockParamsProvider = mock(SqliteParamsProvider.class);
        HashMap<String, String> params = new HashMap<>();
        params.put("test.unknown", "value");
        params.put("i2r.bip.status", "1");

        when(mockParamsProvider.fetchAndResetUpdatedParameters()).thenReturn(params);

        DbUpdateWatcherAdapter adapter = new DbUpdateWatcherAdapter(mockParamsProvider, watchedFile.toString());

        List<ConfigurationValue> result = adapter.mapParametersToConfigurationValues(params);

        assertThat(result).hasSize(1); // Only the known parameter should be mapped
        ConfigurationValue configValue = result.get(0);
        assertThat(configValue.parameter()).isEqualTo(BipParameter.BipState);
        assertThat(configValue.value()).isEqualTo("1");
    }
}
