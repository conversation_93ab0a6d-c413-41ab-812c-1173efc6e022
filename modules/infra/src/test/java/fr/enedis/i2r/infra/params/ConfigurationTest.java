package fr.enedis.i2r.infra.params;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import java.util.HashMap;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import fr.enedis.i2r.comsi.errors.InvalidParameterException;
import fr.enedis.i2r.comsi.errors.RequiredParameterMissingException;

class ConfigurationTest {

    private Configuration configuration;
    private HashMap<String, String> parameters;

    @BeforeEach
    void setUp() {
        parameters = new HashMap<>();
        parameters.put("test.string", "value");
        parameters.put("test.number", "42");
        parameters.put("test.boolean", "true");
        parameters.put("test.invalid", "invalid_value");
        configuration = new Configuration(parameters);
    }

    @Test
    void la_recuperation_de_parametre_existant_retourne_la_valeur() {
        Optional<String> result = configuration.get("test.string");

        assertThat(result).isPresent();
        assertThat(result.get()).isEqualTo("value");
    }

    @Test
    void la_recuperation_de_parametre_inexistant_retourne_empty() {
        Optional<String> result = configuration.get("nonexistent.key");

        assertThat(result).isEmpty();
    }

    @Test
    void le_parsing_de_parametre_requis_existant_reussit() throws RequiredParameterMissingException, InvalidParameterException {
        RequiredParameter<String> stringParam = new RequiredParameter<>("test.string", value -> value);

        String result = configuration.parse(stringParam);

        assertThat(result).isEqualTo("value");
    }

    @Test
    void le_parsing_de_parametre_requis_inexistant_leve_une_exception() {
        RequiredParameter<String> missingParam = new RequiredParameter<>("missing.key", value -> value);

        assertThatThrownBy(() -> configuration.parse(missingParam))
            .isInstanceOf(RequiredParameterMissingException.class);
    }

    @Test
    void le_parsing_de_parametre_requis_avec_parser_qui_echoue_leve_une_exception() {
        RequiredParameter<Integer> invalidParam = new RequiredParameter<>("test.invalid", value -> {
            throw new InvalidParameterException("Invalid format");
        });

        assertThatThrownBy(() -> configuration.parse(invalidParam))
            .isInstanceOf(InvalidParameterException.class)
            .hasMessageContaining("Invalid format");
    }

    @Test
    void le_parsing_avec_defaut_de_parametre_existant_retourne_la_valeur() {
        ParameterWithDefault<String> stringParam = new ParameterWithDefault<>("test.string", value -> value, "default");

        String result = configuration.parseOrDefault(stringParam);

        assertThat(result).isEqualTo("value");
    }

    @Test
    void le_parsing_avec_defaut_de_parametre_inexistant_retourne_la_valeur_par_defaut() {
        ParameterWithDefault<String> missingParam = new ParameterWithDefault<>("missing.key", value -> value, "default");

        String result = configuration.parseOrDefault(missingParam);

        assertThat(result).isEqualTo("default");
    }

    @Test
    void le_parsing_avec_defaut_de_parametre_null_retourne_la_valeur_par_defaut() {
        parameters.put("null.key", null);
        configuration = new Configuration(parameters);
        ParameterWithDefault<String> nullParam = new ParameterWithDefault<>("null.key", value -> value, "default");

        String result = configuration.parseOrDefault(nullParam);

        assertThat(result).isEqualTo("default");
    }

    @Test
    void le_parsing_avec_defaut_qui_echoue_retourne_la_valeur_par_defaut() {
        ParameterWithDefault<Integer> invalidParam = new ParameterWithDefault<>("test.invalid", value -> {
            throw new InvalidParameterException("Invalid format");
        }, 100);

        Integer result = configuration.parseOrDefault(invalidParam);

        assertThat(result).isEqualTo(100);
    }

    @Test
    void le_parsing_avec_defaut_gere_les_exceptions_runtime() {
        ParameterWithDefault<Integer> runtimeErrorParam = new ParameterWithDefault<>("test.string", value -> {
            throw new RuntimeException("Runtime error");
        }, 200);

        Integer result = configuration.parseOrDefault(runtimeErrorParam);

        assertThat(result).isEqualTo(200);
    }

    @Test
    void le_parsing_de_nombre_entier_fonctionne() throws RequiredParameterMissingException, InvalidParameterException {
        RequiredParameter<Integer> numberParam = new RequiredParameter<>("test.number", value -> Integer.parseInt(value));

        Integer result = configuration.parse(numberParam);

        assertThat(result).isEqualTo(42);
    }

    @Test
    void le_parsing_de_booleen_fonctionne() throws RequiredParameterMissingException, InvalidParameterException {
        RequiredParameter<Boolean> booleanParam = new RequiredParameter<>("test.boolean", value -> Boolean.parseBoolean(value));

        Boolean result = configuration.parse(booleanParam);

        assertThat(result).isTrue();
    }
}
