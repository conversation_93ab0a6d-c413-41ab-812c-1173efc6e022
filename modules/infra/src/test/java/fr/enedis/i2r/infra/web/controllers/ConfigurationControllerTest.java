package fr.enedis.i2r.infra.web.controllers;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import fr.enedis.i2r.comsi.ComSiConfiguration;
import fr.enedis.i2r.comsi.ConfigurationBoitier;
import fr.enedis.i2r.comsi.errors.rest.InvalidQueryParamsException;
import fr.enedis.i2r.comsi.ports.BoardManagerPort;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.comsi.ports.ModemManagerPort;
import fr.enedis.i2r.comsi.ports.SecurityModulePort;
import fr.enedis.i2r.comsi.ports.si.SiClientPort;
import fr.enedis.i2r.infra.rest.CustomComSiConfiguration;
import io.javalin.http.Context;
import io.javalin.http.HttpStatus;

class ConfigurationControllerTest {

    @Mock
    private ComSiConfiguration comSiConfiguration;
    @Mock
    private ComsiParametersPort parametersPort;
    @Mock
    private BoardManagerPort boardManagerPort;
    @Mock
    private SecurityModulePort moduleSecuritePort;
    @Mock
    private ModemManagerPort modemManagerPort;
    @Mock
    private SiClientPort siClientPort;
    @Mock
    private Context context;

    private ConfigurationController controller;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        controller = new ConfigurationController(
            comSiConfiguration,
            parametersPort,
            boardManagerPort,
            moduleSecuritePort,
            modemManagerPort,
            siClientPort
        );
    }

    @Test
    void la_configuration_boitier_est_creee_avec_les_bonnes_valeurs() throws Exception {
        when(context.queryParam("depth")).thenReturn("25");
        when(context.status(any(HttpStatus.class))).thenReturn(context);
        when(parametersPort.getConfigurationHash()).thenReturn("HASH_123");
        when(boardManagerPort.getAds()).thenReturn("ADS_456");
        when(moduleSecuritePort.getIdms()).thenReturn("IDMS_789");
        when(modemManagerPort.getIccid()).thenReturn("ICCID_012");

        CustomComSiConfiguration customConfig = new CustomComSiConfiguration();
        when(comSiConfiguration.netmaskSize()).thenReturn(customConfig.tailleDeMasqueReseau);
        when(comSiConfiguration.pingRetryLimit()).thenReturn(customConfig.tentativesLorsDUnPing);
        when(comSiConfiguration.parseDatacenterConfiguration()).thenReturn(customConfig.build().parseDatacenterConfiguration());
        when(comSiConfiguration.pingTimeout()).thenReturn(customConfig.dureeMaximumDePing);
        when(comSiConfiguration.modemResetsLimitBeforeAo3Reboot()).thenReturn(customConfig.limiteDeResetsModemAvantRebootBoitier);
        when(comSiConfiguration.bipStatus()).thenReturn(customConfig.bipStatus);

        ArgumentCaptor<ConfigurationBoitier> configCaptor = ArgumentCaptor.forClass(ConfigurationBoitier.class);

        controller.getConfiguration(context);

        verify(siClientPort).sendConfigurationBoitier(configCaptor.capture());
        ConfigurationBoitier capturedConfig = configCaptor.getValue();

        assertThat(capturedConfig.configurationHash()).isEqualTo("HASH_123");
        assertThat(capturedConfig.ads()).isEqualTo("ADS_456");
        assertThat(capturedConfig.idms()).isEqualTo("IDMS_789");
        assertThat(capturedConfig.iccid()).isEqualTo("ICCID_012");
        assertThat(capturedConfig.nbBitsMasqueIpv4()).isEqualTo(24);
        assertThat(capturedConfig.pingRetryLimit()).isEqualTo(3);
    }

    @Test
    void la_recuperation_de_configuration_sans_parametre_depth_utilise_la_valeur_par_defaut() throws Exception {
        when(context.queryParam("depth")).thenReturn(null);
        when(context.status(any(HttpStatus.class))).thenReturn(context);
        when(parametersPort.getConfigurationHash()).thenReturn("TEST_HASH");
        when(boardManagerPort.getAds()).thenReturn("TEST_ADS");
        when(moduleSecuritePort.getIdms()).thenReturn("TEST_IDMS");
        when(modemManagerPort.getIccid()).thenReturn("TEST_ICCID");

        CustomComSiConfiguration customConfig = new CustomComSiConfiguration();
        when(comSiConfiguration.netmaskSize()).thenReturn(customConfig.tailleDeMasqueReseau);
        when(comSiConfiguration.pingRetryLimit()).thenReturn(customConfig.tentativesLorsDUnPing);
        when(comSiConfiguration.parseDatacenterConfiguration()).thenReturn(customConfig.build().parseDatacenterConfiguration());
        when(comSiConfiguration.pingTimeout()).thenReturn(customConfig.dureeMaximumDePing);
        when(comSiConfiguration.modemResetsLimitBeforeAo3Reboot()).thenReturn(customConfig.limiteDeResetsModemAvantRebootBoitier);
        when(comSiConfiguration.bipStatus()).thenReturn(customConfig.bipStatus);

        controller.getConfiguration(context);

        verify(context).status(HttpStatus.OK);
        verify(siClientPort).sendConfigurationBoitier(any(ConfigurationBoitier.class));
    }

    @Test
    void la_recuperation_de_configuration_avec_parametre_depth_valide_fonctionne() throws Exception {
        when(context.queryParam("depth")).thenReturn("50");
        when(context.status(any(HttpStatus.class))).thenReturn(context);
        when(parametersPort.getConfigurationHash()).thenReturn("TEST_HASH");
        when(boardManagerPort.getAds()).thenReturn("TEST_ADS");
        when(moduleSecuritePort.getIdms()).thenReturn("TEST_IDMS");
        when(modemManagerPort.getIccid()).thenReturn("TEST_ICCID");

        CustomComSiConfiguration customConfig = new CustomComSiConfiguration();
        when(comSiConfiguration.netmaskSize()).thenReturn(customConfig.tailleDeMasqueReseau);
        when(comSiConfiguration.pingRetryLimit()).thenReturn(customConfig.tentativesLorsDUnPing);
        when(comSiConfiguration.parseDatacenterConfiguration()).thenReturn(customConfig.build().parseDatacenterConfiguration());
        when(comSiConfiguration.pingTimeout()).thenReturn(customConfig.dureeMaximumDePing);
        when(comSiConfiguration.modemResetsLimitBeforeAo3Reboot()).thenReturn(customConfig.limiteDeResetsModemAvantRebootBoitier);
        when(comSiConfiguration.bipStatus()).thenReturn(customConfig.bipStatus);

        controller.getConfiguration(context);

        verify(context).status(HttpStatus.OK);
        verify(siClientPort).sendConfigurationBoitier(any(ConfigurationBoitier.class));
    }

    @Test
    void la_recuperation_de_configuration_avec_depth_zero_fonctionne() throws Exception {
        when(context.queryParam("depth")).thenReturn("0");
        when(context.status(any(HttpStatus.class))).thenReturn(context);
        when(parametersPort.getConfigurationHash()).thenReturn("TEST_HASH");
        when(boardManagerPort.getAds()).thenReturn("TEST_ADS");
        when(moduleSecuritePort.getIdms()).thenReturn("TEST_IDMS");
        when(modemManagerPort.getIccid()).thenReturn("TEST_ICCID");

        CustomComSiConfiguration customConfig = new CustomComSiConfiguration();
        when(comSiConfiguration.netmaskSize()).thenReturn(customConfig.tailleDeMasqueReseau);
        when(comSiConfiguration.pingRetryLimit()).thenReturn(customConfig.tentativesLorsDUnPing);
        when(comSiConfiguration.parseDatacenterConfiguration()).thenReturn(customConfig.build().parseDatacenterConfiguration());
        when(comSiConfiguration.pingTimeout()).thenReturn(customConfig.dureeMaximumDePing);
        when(comSiConfiguration.modemResetsLimitBeforeAo3Reboot()).thenReturn(customConfig.limiteDeResetsModemAvantRebootBoitier);
        when(comSiConfiguration.bipStatus()).thenReturn(customConfig.bipStatus);

        controller.getConfiguration(context);

        verify(context).status(HttpStatus.OK);
        verify(siClientPort).sendConfigurationBoitier(any(ConfigurationBoitier.class));
    }

    @Test
    void la_recuperation_de_configuration_avec_parametre_depth_invalide_leve_une_exception() {
        when(context.queryParam("depth")).thenReturn("invalid_number");

        assertThatThrownBy(() -> controller.getConfiguration(context))
            .isInstanceOf(InvalidQueryParamsException.class)
            .hasMessage("Parametre(s) de la query invalide(s): Paramètre depth invalide: doit être un nombre entier");
    }

    @Test
    void tous_les_ports_sont_appeles_pour_recuperer_les_donnees() throws Exception {
        when(context.queryParam("depth")).thenReturn(null);
        when(context.status(any(HttpStatus.class))).thenReturn(context);
        when(parametersPort.getConfigurationHash()).thenReturn("TEST_HASH");
        when(boardManagerPort.getAds()).thenReturn("TEST_ADS");
        when(moduleSecuritePort.getIdms()).thenReturn("TEST_IDMS");
        when(modemManagerPort.getIccid()).thenReturn("TEST_ICCID");

        CustomComSiConfiguration customConfig = new CustomComSiConfiguration();
        when(comSiConfiguration.netmaskSize()).thenReturn(customConfig.tailleDeMasqueReseau);
        when(comSiConfiguration.pingRetryLimit()).thenReturn(customConfig.tentativesLorsDUnPing);
        when(comSiConfiguration.parseDatacenterConfiguration()).thenReturn(customConfig.build().parseDatacenterConfiguration());
        when(comSiConfiguration.pingTimeout()).thenReturn(customConfig.dureeMaximumDePing);
        when(comSiConfiguration.modemResetsLimitBeforeAo3Reboot()).thenReturn(customConfig.limiteDeResetsModemAvantRebootBoitier);
        when(comSiConfiguration.bipStatus()).thenReturn(customConfig.bipStatus);

        controller.getConfiguration(context);

        verify(parametersPort).getConfigurationHash();
        verify(boardManagerPort).getAds();
        verify(moduleSecuritePort).getIdms();
        verify(modemManagerPort).getIccid();
    }
}
