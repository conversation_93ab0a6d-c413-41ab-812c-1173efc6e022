package fr.enedis.i2r.infra.rest;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import com.google.common.net.HttpHeaders;
import com.google.common.net.MediaType;

public class SiHeaders {
    public interface Constants {
        // Enedis headers
        public static final String API_VERSION_HEADER = "X-Erdf-Api-Version";
        public static final String API_VERSION_VALUE = "2.0";
        public static final String IDMS_HEADER = "X-Idms";
        public static final String HASH_HEADER = "X-ERDF-HASH";
        public static final String CCMA_CORRELATION_ID = "ccma-correlation-id";
        public static final String ZIP_FORMAT = "gzip";
    }

    public String ads;
    public String hash;

    public SiHeaders(String ads, String hash) {
        this.ads = ads;
        this.hash = hash;
    }

    public Map<String, String> toMap() {
        HashMap<String, String> headers = new HashMap<String, String>();

        headers.put(HttpHeaders.CONTENT_TYPE, MediaType.JSON_UTF_8.toString());
        headers.put(HttpHeaders.ACCEPT, MediaType.JSON_UTF_8.withoutParameters().toString());
        headers.put(HttpHeaders.CONTENT_ENCODING, SiHeaders.Constants.ZIP_FORMAT);
        headers.put(HttpHeaders.ACCEPT_ENCODING, SiHeaders.Constants.ZIP_FORMAT);
        headers.put(HttpHeaders.TE, SiHeaders.Constants.ZIP_FORMAT);
        headers.put(SiHeaders.Constants.API_VERSION_HEADER, SiHeaders.Constants.API_VERSION_VALUE);
        // l'identifiant DU MESSAGE de corrélation au format UUID
        headers.put(SiHeaders.Constants.CCMA_CORRELATION_ID, UUID.randomUUID().toString());
        headers.put(SiHeaders.Constants.HASH_HEADER, hash);

        return headers;
    }
}
