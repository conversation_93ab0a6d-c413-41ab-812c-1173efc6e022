package fr.enedis.i2r.infra.params;

import static java.util.Collections.emptyList;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.params.ConfigurationValue;
import fr.enedis.i2r.comsi.ports.ConfigurationUpdateWatcherPort;

public class DbUpdateWatcherAdapter implements ConfigurationUpdateWatcherPort {

    private static final Logger logger = LoggerFactory.getLogger(DbUpdateWatcherAdapter.class);
    private final Path databaseFile;
    private SqliteParamsProvider paramsProvider;
    private Instant knownLastDatabaseUpdate;

    public DbUpdateWatcherAdapter(SqliteParamsProvider paramsProvider, String databasePath) throws IOException {
        this.paramsProvider = paramsProvider;

        this.databaseFile = Paths.get(databasePath);
        this.knownLastDatabaseUpdate = Files.getLastModifiedTime(databaseFile).toInstant();

        logger.info("Starting to watch params db file: {}", databaseFile.toString());
    }

    @Override
    public List<ConfigurationValue> fetchUpdatedValues() {
        if (!hasDatabaseFileBeenUpdated()) {
            return emptyList();
        }

        logger.info("Changement détecté de la base de données");

        HashMap<String, String> updatedParameters = paramsProvider.fetchAndResetUpdatedParameters();

        return mapParametersToConfigurationValues(updatedParameters);
    }

    private boolean hasDatabaseFileBeenUpdated() {
        try {
            var currentLastDatabaseUpdate = Files.getLastModifiedTime(databaseFile).toInstant();

            if(currentLastDatabaseUpdate.isAfter(knownLastDatabaseUpdate)) {
                knownLastDatabaseUpdate = currentLastDatabaseUpdate;
                return true;
            }
        } catch(IOException e) {
            logger.error("erreur lors de l'accès au fichier de base de données", e);
        }

        return false;
    }

    public List<ConfigurationValue> mapParametersToConfigurationValues(HashMap<String, String> rawParams) {
        return rawParams.entrySet().stream()
            .map(entry -> ConfigurationValue.from(entry.getKey(), entry.getValue()))
            .flatMap(Optional::stream)
            .toList();
    }
}
