package fr.enedis.i2r.infra.rest.si.ao2;

import java.util.ArrayList;
import java.util.List;

public class Device extends Base {

    private List<Base> objects = new ArrayList<>();

    public Device() {
        this.setClazz("Container");
        this.setName("device");
    }

    public List<Base> getObjects() {
        return this.objects;
    }

    public int getNb() {
        // TODO: The SI expects this value to be 7, however for the time being, the configuration
        //       we build doesn't have every objects expected by the SI, so we hardcode the return
        //       with "7", but this should be changed in the future and be replaced with this.objects.size()
        return 7;
    }

    public void addObject(Base object) {
        this.objects.add(object);
    }
}
