package fr.enedis.i2r.comsi.status;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import fr.enedis.i2r.comsi.errors.rest.InvalidRequestHeadersException;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.system.ports.SystemModulePort;

class BipStatusManagerTest {

    @Mock
    private ComsiParametersPort parametersPort;

    @Mock
    private SystemModulePort systemModule;

    private BipStatusManager bipStatusManager;

    private static final String VALID_IDMS = "test-idms";
    private static final String VALID_CONFIG_HASH = "abc123";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        bipStatusManager = new BipStatusManager(parametersPort, VALID_IDMS, systemModule);
    }

    @Test
    void le_changement_d_etat_avec_parametres_valides_reussit() throws InvalidRequestHeadersException {
        when(parametersPort.getConfigurationHash()).thenReturn(VALID_CONFIG_HASH);
        BipStatus newStatus = BipStatus.STABLE;

        bipStatusManager.changeBipStatus(newStatus, VALID_IDMS, VALID_CONFIG_HASH);

        verify(parametersPort).updateBipStatus(newStatus);
        verify(systemModule).activateSecondaryServices();
        verify(systemModule).startSecondaryServices();
    }

    @Test
    void l_idms_incorrect_leve_une_exception() {
        String wrongIdms = "wrong-idms";
        when(parametersPort.getConfigurationHash()).thenReturn(VALID_CONFIG_HASH);

        assertThatThrownBy(() ->
            bipStatusManager.changeBipStatus(BipStatus.STABLE, wrongIdms, VALID_CONFIG_HASH))
            .isInstanceOf(InvalidRequestHeadersException.class)
            .hasMessageContaining("l'idms de la configuration ciblé (wrong-idms) ne correspond pas à celui du boitier (test-idms)");
    }

    @Test
    void l_utilisation_d_un_hash_incorrect_pour_l_activation_leve_une_execption() {
        String wrongConfigHash = "wrong-hash";
        when(parametersPort.getConfigurationHash()).thenReturn(VALID_CONFIG_HASH);

        assertThatThrownBy(() ->
            bipStatusManager.changeBipStatus(BipStatus.STABLE, VALID_IDMS, wrongConfigHash))
            .isInstanceOf(InvalidRequestHeadersException.class)
            .hasMessageContaining("le hash de la configuration ciblé (wrong-hash) ne correspond pas à celui du boitier (abc123)");
    }

    @Test
    void la_comparaison_idms_ignore_la_casse() throws InvalidRequestHeadersException {
        when(parametersPort.getConfigurationHash()).thenReturn(VALID_CONFIG_HASH);
        String upperCaseIdms = VALID_IDMS.toUpperCase();

        bipStatusManager.changeBipStatus(BipStatus.STABLE, upperCaseIdms, VALID_CONFIG_HASH);

        verify(parametersPort).updateBipStatus(BipStatus.STABLE);
        verify(systemModule).activateSecondaryServices();
        verify(systemModule).startSecondaryServices();
    }

    @Test
    void la_comparaison_hash_ignore_la_casse() throws InvalidRequestHeadersException {
        when(parametersPort.getConfigurationHash()).thenReturn(VALID_CONFIG_HASH);
        String upperCaseConfigHash = VALID_CONFIG_HASH.toUpperCase();

        bipStatusManager.changeBipStatus(BipStatus.STABLE, VALID_IDMS, upperCaseConfigHash);

        verify(parametersPort).updateBipStatus(BipStatus.STABLE);
        verify(systemModule).activateSecondaryServices();
        verify(systemModule).startSecondaryServices();
    }

    @Test
    void le_changement_vers_etat_init_fonctionne() throws InvalidRequestHeadersException {
        when(parametersPort.getConfigurationHash()).thenReturn(VALID_CONFIG_HASH);

        bipStatusManager.changeBipStatus(BipStatus.INIT, VALID_IDMS, VALID_CONFIG_HASH);

        verify(parametersPort).updateBipStatus(BipStatus.INIT);
        verify(systemModule).activateSecondaryServices();
        verify(systemModule).startSecondaryServices();
    }

    @Test
    void l_idms_null_leve_une_exception() {
        when(parametersPort.getConfigurationHash()).thenReturn(VALID_CONFIG_HASH);

        assertThatThrownBy(() ->
            bipStatusManager.changeBipStatus(BipStatus.STABLE, null, VALID_CONFIG_HASH))
            .isInstanceOf(NullPointerException.class);
    }

    @Test
    void le_hash_null_leve_une_exception() {
        when(parametersPort.getConfigurationHash()).thenReturn(VALID_CONFIG_HASH);

        assertThatThrownBy(() ->
            bipStatusManager.changeBipStatus(BipStatus.STABLE, VALID_IDMS, null))
            .isInstanceOf(InvalidRequestHeadersException.class)
            .hasMessageContaining("le hash de la configuration ciblé (null) ne correspond pas à celui du boitier");
    }

    @Test
    void l_idms_vide_leve_une_exception() {
        when(parametersPort.getConfigurationHash()).thenReturn(VALID_CONFIG_HASH);

        assertThatThrownBy(() ->
            bipStatusManager.changeBipStatus(BipStatus.STABLE, "", VALID_CONFIG_HASH))
            .isInstanceOf(InvalidRequestHeadersException.class);
    }

    @Test
    void le_hash_vide_leve_une_exception() {
        when(parametersPort.getConfigurationHash()).thenReturn(VALID_CONFIG_HASH);

        assertThatThrownBy(() ->
            bipStatusManager.changeBipStatus(BipStatus.STABLE, VALID_IDMS, ""))
            .isInstanceOf(InvalidRequestHeadersException.class);
    }
}
