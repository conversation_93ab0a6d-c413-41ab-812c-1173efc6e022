package fr.enedis.i2r.comsi;

import static org.assertj.core.api.Assertions.assertThat;

import java.time.Duration;
import java.time.LocalTime;
import java.util.Optional;

import org.junit.jupiter.api.Test;

import fr.enedis.i2r.comsi.status.BipStatus;

class DatacenterConfigurationTest {

    private static final IpAddress NOE_IP = new IpAddress("***********");
    private static final IpAddress PACY_IP = new IpAddress("***********");

    @Test
    void la_configuration_avec_pacy_primaire_est_correcte() {
        ComSiConfiguration comSiConfig = createComSiConfiguration(Datacenter.PACY);

        DatacenterConfiguration datacenterConfig = DatacenterConfiguration.from(comSiConfig);

        assertThat(datacenterConfig.primaryIp()).isEqualTo(PACY_IP);
        assertThat(datacenterConfig.secondaryIp()).isEqualTo(NOE_IP);
    }

    @Test
    void la_configuration_avec_noe_primaire_est_correcte() {
        ComSiConfiguration comSiConfig = createComSiConfiguration(Datacenter.NOE);

        DatacenterConfiguration datacenterConfig = DatacenterConfiguration.from(comSiConfig);

        assertThat(datacenterConfig.primaryIp()).isEqualTo(NOE_IP);
        assertThat(datacenterConfig.secondaryIp()).isEqualTo(PACY_IP);
    }

    @Test
    void les_adresses_ip_personnalisees_sont_respectees() {
        IpAddress customNoeIp = new IpAddress("***********");
        IpAddress customPacyIp = new IpAddress("***********");

        ComSiConfiguration comSiConfig = new ComSiConfiguration(
            Duration.ofMinutes(5),
            Duration.ofMinutes(1),
            Duration.ofSeconds(5),
            customNoeIp,
            customPacyIp,
            3,
            2,
            24,
            3,
            Duration.ofSeconds(1),
            Datacenter.PACY,
            BipStatus.INIT,
            LocalTime.parse("01:00:00"),
            Duration.ofMinutes(120),
            Optional.empty()
        );

        DatacenterConfiguration datacenterConfig = DatacenterConfiguration.from(comSiConfig);

        assertThat(datacenterConfig.primaryIp()).isEqualTo(customPacyIp);
        assertThat(datacenterConfig.secondaryIp()).isEqualTo(customNoeIp);
    }

    @Test
    void l_echange_primaire_secondaire_fonctionne_pour_noe() {
        IpAddress customNoeIp = new IpAddress("**********");
        IpAddress customPacyIp = new IpAddress("**********");

        ComSiConfiguration comSiConfig = new ComSiConfiguration(
            Duration.ofMinutes(5),
            Duration.ofMinutes(1),
            Duration.ofSeconds(5),
            customNoeIp,
            customPacyIp,
            3,
            2,
            24,
            3,
            Duration.ofSeconds(1),
            Datacenter.NOE,
            BipStatus.INIT,
            LocalTime.parse("01:00:00"),
            Duration.ofMinutes(120),
            Optional.empty()
        );

        DatacenterConfiguration datacenterConfig = DatacenterConfiguration.from(comSiConfig);

        assertThat(datacenterConfig.primaryIp()).isEqualTo(customNoeIp);
        assertThat(datacenterConfig.secondaryIp()).isEqualTo(customPacyIp);
    }

    private ComSiConfiguration createComSiConfiguration(Datacenter primaryDatacenter) {
        return new ComSiConfiguration(
            Duration.ofMinutes(5),
            Duration.ofMinutes(1),
            Duration.ofSeconds(5),
            NOE_IP,
            PACY_IP,
            3,
            2,
            24,
            3,
            Duration.ofSeconds(1),
            primaryDatacenter,
            BipStatus.INIT,
            LocalTime.parse("01:00:00"),
            Duration.ofMinutes(120),
            Optional.empty()
        );
    }
}
