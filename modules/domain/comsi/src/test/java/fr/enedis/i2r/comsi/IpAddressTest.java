package fr.enedis.i2r.comsi;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import org.junit.jupiter.api.Test;

class IpAddressTest {

    @Test
    void le_calcul_de_masque_reseau_24_bits_est_correct() {
        IpAddress ipAddress = new IpAddress("*************");

        int netmask = ipAddress.getNetmask(24);

        int expected = (192 << 24) | (168 << 16) | (1 << 8) | 0;
        assertThat(netmask).isEqualTo(expected);
    }

    @Test
    void le_calcul_de_masque_reseau_16_bits_est_correct() {
        IpAddress ipAddress = new IpAddress("**********");

        int netmask = ipAddress.getNetmask(16);

        int expected = (10 << 24) | (0 << 16) | (0 << 8) | 0;
        assertThat(netmask).isEqualTo(expected);
    }

    @Test
    void le_calcul_de_masque_reseau_30_bits_est_correct() {
        IpAddress ipAddress = new IpAddress("********");

        int netmask = ipAddress.getNetmask(30);

        int expected = (10 << 24) | (0 << 16) | (0 << 8) | 4;
        assertThat(netmask).isEqualTo(expected);
    }

    @Test
    void le_masque_zero_bit_retourne_l_adresse_complete() {
        IpAddress ipAddress = new IpAddress("***********");

        int netmask = ipAddress.getNetmask(0);

        int expected = (192 << 24) | (168 << 16) | (1 << 8) | 1;
        assertThat(netmask).isEqualTo(expected);
    }

    @Test
    void le_masque_32_bits_retourne_l_adresse_exacte() {
        IpAddress ipAddress = new IpAddress("***********");

        int netmask = ipAddress.getNetmask(32);

        int expected = (192 << 24) | (168 << 16) | (1 << 8) | 1;
        assertThat(netmask).isEqualTo(expected);
    }

    @Test
    void le_format_ip_invalide_leve_une_exception() {
        IpAddress ipAddress = new IpAddress("invalid.ip.format");

        assertThatThrownBy(() -> ipAddress.getNetmask(24))
            .isInstanceOf(NumberFormatException.class);
    }

    @Test
    void l_ip_incomplete_est_geree_correctement() {
        IpAddress ipAddress = new IpAddress("192.168.1");

        int netmask = ipAddress.getNetmask(24);

        int expected = (192 << 24) | (168 << 16) | (1 << 8) | 0;
        expected = expected & (-1 << (32 - 24));
        assertThat(netmask).isEqualTo(expected);
    }

    @Test
    void les_zeros_en_tete_sont_geres_correctement() {
        IpAddress ipAddress = new IpAddress("***************");

        int netmask = ipAddress.getNetmask(24);

        int expected = (192 << 24) | (168 << 16) | (1 << 8) | 0;
        assertThat(netmask).isEqualTo(expected);
    }

    @Test
    void les_differentes_tailles_de_masque_sont_coherentes() {
        IpAddress ipAddress = new IpAddress("***********");

        assertThat(ipAddress.getNetmask(8)).isEqualTo(10 << 24);
        assertThat(ipAddress.getNetmask(16)).isEqualTo((10 << 24) | (20 << 16));
        assertThat(ipAddress.getNetmask(24)).isEqualTo((10 << 24) | (20 << 16) | (30 << 8));
    }
}
