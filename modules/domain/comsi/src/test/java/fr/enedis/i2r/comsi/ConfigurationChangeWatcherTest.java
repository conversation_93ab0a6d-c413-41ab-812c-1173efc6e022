package fr.enedis.i2r.comsi;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import fr.enedis.i2r.comsi.params.BipParameter;
import fr.enedis.i2r.comsi.params.ConfigurationValue;
import fr.enedis.i2r.comsi.ports.ConfigurationUpdateWatcherPort;
import fr.enedis.i2r.comsi.ports.si.SiConfigurationNotifierPort;
import fr.enedis.i2r.comsi.status.BipStatus;

class ConfigurationChangeWatcherTest {

    @Mock
    private ConfigurationUpdateWatcherPort configurationUpdateWatcher;

    @Mock
    private SiConfigurationNotifierPort siNotifierPort;

    private ConfigurationChangeWatcher watcher;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        watcher = new ConfigurationChangeWatcher(configurationUpdateWatcher, siNotifierPort);
    }

    @Test
    void les_changements_de_configuration_surveillee_notifient_le_si() throws Exception {
        ConfigurationValue watchedValue = new ConfigurationValue(BipParameter.BipState, "1");
        ConfigurationValue nonBipStateValue = new ConfigurationValue(BipParameter.PingPeriodInMs, "5000");

        when(configurationUpdateWatcher.fetchUpdatedValues())
            .thenReturn(Arrays.asList(watchedValue, nonBipStateValue));

        watcher.run();

        verify(siNotifierPort).notifyStateChange(BipStatus.INIT);
    }

    @Test
    void les_parametres_non_bip_state_ne_notifient_pas_le_si() throws Exception {
        ConfigurationValue nonBipStateValue = new ConfigurationValue(BipParameter.PingPeriodInMs, "5000");

        when(configurationUpdateWatcher.fetchUpdatedValues())
            .thenReturn(Collections.singletonList(nonBipStateValue));

        watcher.run();

        verify(siNotifierPort, never()).notifyStateChange(any());
    }

    @Test
    void les_changements_vides_ne_notifient_pas_le_si() throws Exception {
        when(configurationUpdateWatcher.fetchUpdatedValues())
            .thenReturn(Collections.emptyList());

        watcher.run();

        verify(siNotifierPort, never()).notifyStateChange(any());
    }

    @Test
    void le_changement_vers_etat_stable_notifie_le_si() throws Exception {
        ConfigurationValue bipStateValue = new ConfigurationValue(BipParameter.BipState, "2");

        when(configurationUpdateWatcher.fetchUpdatedValues())
            .thenReturn(Collections.singletonList(bipStateValue));

        watcher.run();

        verify(siNotifierPort).notifyStateChange(BipStatus.STABLE);
    }

    @Test
    void les_changements_multiples_d_etat_sont_tous_notifies() throws Exception {
        List<ConfigurationValue> values = Arrays.asList(
            new ConfigurationValue(BipParameter.BipState, "1"),
            new ConfigurationValue(BipParameter.BipState, "2")
        );

        when(configurationUpdateWatcher.fetchUpdatedValues()).thenReturn(values);

        watcher.run();

        verify(siNotifierPort).notifyStateChange(BipStatus.INIT);
        verify(siNotifierPort).notifyStateChange(BipStatus.STABLE);
    }

    @Test
    void les_erreurs_de_recuperation_sont_gerees_sans_exception() throws Exception {
        when(configurationUpdateWatcher.fetchUpdatedValues())
            .thenThrow(new RuntimeException("Database error"));

        watcher.run();

        verify(siNotifierPort, never()).notifyStateChange(any());
    }

    @Test
    void les_erreurs_de_notification_si_sont_gerees_sans_exception() throws Exception {
        ConfigurationValue bipStateValue = new ConfigurationValue(BipParameter.BipState, "1");

        when(configurationUpdateWatcher.fetchUpdatedValues())
            .thenReturn(Collections.singletonList(bipStateValue));

        doThrow(new RuntimeException("SI notification failed"))
            .when(siNotifierPort).notifyStateChange(any());

        watcher.run();

        verify(siNotifierPort).notifyStateChange(BipStatus.INIT);
    }

    @Test
    void les_valeurs_d_etat_invalides_sont_ignorees() throws Exception {
        ConfigurationValue invalidStateValue = new ConfigurationValue(BipParameter.BipState, "invalid");

        when(configurationUpdateWatcher.fetchUpdatedValues())
            .thenReturn(Collections.singletonList(invalidStateValue));

        watcher.run();

        verify(siNotifierPort, never()).notifyStateChange(any());
    }

    @Test
    void les_codes_d_etat_inconnus_sont_ignores() throws Exception {
        ConfigurationValue unknownStateValue = new ConfigurationValue(BipParameter.BipState, "999");

        when(configurationUpdateWatcher.fetchUpdatedValues())
            .thenReturn(Collections.singletonList(unknownStateValue));

        watcher.run();

        verify(siNotifierPort, never()).notifyStateChange(any());
    }


}
