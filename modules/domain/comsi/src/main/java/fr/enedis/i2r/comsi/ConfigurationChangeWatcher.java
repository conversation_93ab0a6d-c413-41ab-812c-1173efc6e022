package fr.enedis.i2r.comsi;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.params.BipParameter;
import fr.enedis.i2r.comsi.params.ConfigurationValue;
import fr.enedis.i2r.comsi.ports.ConfigurationUpdateWatcherPort;
import fr.enedis.i2r.comsi.ports.si.SiConfigurationNotifierPort;
import fr.enedis.i2r.comsi.status.BipStatus;

public class ConfigurationChangeWatcher implements Runnable {
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationChangeWatcher.class);

    private ConfigurationUpdateWatcherPort configurationUpdateWatcher;
    private SiConfigurationNotifierPort siNotifierPort;

    public ConfigurationChangeWatcher(ConfigurationUpdateWatcherPort configurationUpdateWatcher, SiConfigurationNotifierPort siNotifierPort) {
        this.configurationUpdateWatcher = configurationUpdateWatcher;
        this.siNotifierPort = siNotifierPort;

    }

    @Override
    public void run() {
        try {
            List<ConfigurationValue> updatedValues = this.configurationUpdateWatcher
                .fetchUpdatedValues()
                .stream().filter(confValue -> confValue.parameter().watched)
                .toList();

            for (ConfigurationValue updatedValue: updatedValues) {
                this.notifySi(updatedValue);
            }
        } catch (Throwable e) {
            logger.error("erreur lors de l'écoute d'un changement de configuration", e);
        }
    }

    private void notifySi(ConfigurationValue updatedConfiguration) throws Exception {
        switch (updatedConfiguration.parameter()) {
            case BipParameter.BipState -> handleStateChange(updatedConfiguration.value());
            default -> logger.debug("paramètre non pris en charge {}", updatedConfiguration.parameter().name());
        };
    }

    private void handleStateChange(String newState) throws Exception {
        Integer stateValue = Integer.parseInt(newState);

        BipStatus bipStatus = BipStatus.fromStatusCode(stateValue)
            .orElseThrow(() -> new Exception(String.format("nouvel état du boitier invalide: %d", stateValue)));

        this.siNotifierPort.notifyStateChange(bipStatus);
    }
}
