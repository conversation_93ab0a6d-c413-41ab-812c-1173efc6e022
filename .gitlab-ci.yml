include:
  - project: "dev/commun/templates"
    ref: &CI_TEMPLATE_VERSION "v2"
    file:
      - "templates/java-maven-build.yml"
      - "templates/java-maven-test.yml"
      - "templates/java-maven-sonarqube.yml"
      # - "templates/merge-request-check.yml"

.on_mr_rules:
  rules:
    - if: $CI_MERGE_REQUEST_ID

.on_any_branch:
  rules:
    - if: $MAKE_RELEASE == "false" && $ALWAYS_RUN_EVEN_NO_PR == "true"

default:
  tags:
    - hadock

stages:
  - build
  - test
  # - check-mr

variables:
  KYSS_EXPORT: false
  MAVEN_IMAGE: ${PLACIDE_RELEASES_DOCKER_REPO}.${REPOSITOI_LOCAL_ZONE_HOSTNAME}/maven:3.9.9-jdk21-alpine-enedis-12

build:
  extends: .build-maven
  stage: build
  rules:
    - !reference [.on_mr_rules, rules]
    - !reference [.on_any_branch, rules] 

maven-test:
  stage: test
  extends: .test-maven
  variables:
    MAVEN_GOALS: "verify"
    # Skip E2E tests in CI environment to avoid timeout issues
    CI: "true"
  after_script:
    - |
      for site in $(find . -type d -name target)
      do
        if [ -f "${site}/site/jacoco/jacoco.csv" ]; then
          awk -F"," '{ instructions += $4 + $5; covered += $5 } END { print covered, "/", instructions, "instructions covered"; print 100*covered/instructions, "% covered" }' ${site}/site/jacoco/jacoco.csv
        fi
      done
  artifacts:
    reports:
      junit:
        - "target/surefire-reports/TEST-*.xml"
        - "**/target/surefire-reports/TEST-*.xml"
    paths:
      - "**/target/site/jacoco/"
  rules:
    - !reference [.on_mr_rules, rules]
    - !reference [.on_any_branch, rules] 

🔬 maven-sonar:
  stage: test
  extends: .sonarqube-scan-template
  variables:
    SONAR_PROJECT_KEY: "i2R"
    SONAR_PROJECT_NAME: "${CI_PROJECT_NAME}"
    KYSS_EXPORT: "false"
  allow_failure: true
  rules:
    - !reference [.on_mr_rules, rules]
    - !reference [.on_any_branch, rules] 

# check-min-approvals:
#   extends: .check_min_approvals
#   stage: check-mr
#   variables:
#     MIN_APPROVALS: 1
#   rules:
#     - !reference [.on_mr_rules, rules]

