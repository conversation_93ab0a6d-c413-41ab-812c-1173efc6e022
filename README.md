# I2R

## Vue d'ensemble

I2R est un logiciel embarqué développé par Enedis pour les boîtiers BIP AO3. Ce système assure la communication bidirectionnelle entre les équipements terrain et le SI d'Enedis via le module ComSI (Communication avec le Système d'Information).

### Contexte métier

Les BIP sont des équipements critiques du réseau électrique d'Enedis qui nécessitent :
- Une surveillance en temps réel de leur état et de leurs métriques
- Une configuration centralisée depuis le SI
- Une communication sécurisée et fiable
- Une gestion automatisée des pannes et redémarrages

## Architecture

Le projet suit une **architecture hexagonale** (ports & adapters) garantissant la séparation des préoccupations et la testabilité.

### Architecture évolutive

L'architecture hexagonale permet l'ajout facile de nouveaux adaptateurs :
- Nouveaux protocoles de communication
- Sources de métriques additionnelles
- Systèmes de persistance alternatifs

### Structure des modules

```
i2r/
├── modules/
│   ├── domain/          # Logique métier pure
│   │   ├── comsi/       # Communication avec le SI
│   │   └── system/      # Gestion système et surveillance
│   └── infra/           # Adaptateurs techniques
├── apps/
│   ├── main/            # Application principale
│   ├── si-mock/         # Mock du SI pour les tests
│   └── e2e-tests/       # Tests end-to-end avec Cucumber
```

### Couches architecturales

#### Domain (Logique métier)

**Module ComSI** : Coeur de la communication avec le SI
- Gestion des états du boîtier (INIT, STABLE)
- Envoi de configuration et métriques au SI
- Surveillance des changements de paramètres
- Gestion de la redondance des datacenters

**Module System** : Gestion système et surveillance
- Watchdog multi-niveaux (threads + systemd)
- Gestion des LEDs et signalisation
- Services système (systemd)
- Synchronisation temporelle

#### Infrastructure (Adaptateurs techniques)

**Adaptateurs réseau** :
- Client HTTP/HTTPS avec SSL/TLS
- Communication D-Bus avec le HAL
- Serveur web REST (Javalin)

**Adaptateurs persistance** :
- SQLite pour la configuration
- Fichiers JSONL pour les métriques (Telegraf)

**Adaptateurs système** :
- D-Bus pour l'interaction hardware
- Systemd pour la gestion des services
- Shell pour les commandes système

## Technologies utilisées

### Framework et bibliothèques principales

- **Java 21** : Langage principal avec records et pattern matching
- **Javalin 6.6** : Framework web léger pour l'API REST
- **D-Bus Java** : Communication avec le Hardware Abstraction Layer (HAL)
- **SQLite JDBC** : Base de données embarquée pour la configuration
- **SLF4J + Logback** : Logging structuré
- **Jackson** : Sérialisation JSON
- **Maven** : Build et gestion des dépendances

### Sécurité

- **SSL/TLS** : Chiffrement des communications
- **Certificats X.509** : Authentification mutuelle
- **Keystore/Truststore** : Gestion des certificats

### Surveillance et métriques

- **Telegraf** : Collecte de métriques système et réseau
- **Systemd Watchdog** : Surveillance au niveau OS
- **Thread Watchdog** : Surveillance applicative
- **Graphite + Carbon** : Stockage et visualisation des métriques

## Fonctionnalités principales

### 1. Gestion des états du boîtier

Le boîtier BIP suit un cycle de vie simple mais critique :

- **INIT (1)** : État initial, envoi de la configuration au SI
- **STABLE (2)** : État opérationnel, services secondaires actifs

**Points d'attention** :
- La transition d'état nécessite une validation stricte (IDMS + hash de configuration)
- Seul le SI peut déclencher les changements d'état via l'API REST
- Les services secondaires (Telegraf, Graphite) ne démarrent qu'en état STABLE

### 2. Communication avec le Système d'Information

**Endpoints SI** :
- Configuration du boîtier : `POST /ao2/dm/{idms}/cfg`
- Métriques : `POST /ao2/dm/{idms}/metrics`

**Redondance** : Communication avec datacenter primaire puis secondaire en cas d'échec

**Sécurité** :
- Headers obligatoires : `X-ERDF-API-VERSION`, `x-idms`, `X-ERDF-HASH`
- Compression GZIP
- Corrélation UUID pour le traçage

### 3. API REST locale

**Endpoints disponibles** :

```
GET  /api/i2r/time/sync     # Statut synchronisation temporelle
POST /api/i2r/log/app       # Modification niveau de log
GET  /db/cfg                # Configuration actuelle du boîtier
PUT  /db/cfg/dm             # Changement d'état du boîtier
```

**Documentation** : Swagger UI disponible sur les ports 8081 (HTTP) et 443 (HTTPS)

### 4. Surveillance multi-niveaux

**Thread Watchdog** :
- Surveillance des threads critiques (ComSI, WebServer)
- Timeout configurable (défaut : 90 secondes)
- Redémarrage automatique via systemd en cas de blocage

**Systemd Watchdog** :
- Heartbeat vers systemd toutes les 45 secondes
- Redémarrage du service en cas d'absence de heartbeat
- Intégration avec le Thread Watchdog

**Détection de deadlocks** : Analyse automatique des threads bloqués

### 5. Collecte et envoi de métriques

**Sources de métriques** :
- Telegraf : Métriques système, réseau, modem
- Format JSONL pour la persistance locale
- Parsing automatique des métriques cellulaires (RSRP, RSRQ, SINR, etc.)

**Envoi programmé** :
- Fenêtre configurable (défaut : 01h00-03h00)
- Suppression automatique après envoi réussi
- Gestion des erreurs avec retry

### 6. Gestion de la configuration

**Stockage SQLite** :
- Paramètres de configuration avec hash MD5
- Surveillance des changements en temps réel
- Notification automatique au SI lors de modifications

**Paramètres surveillés** :
- État du boîtier
- Intervalles de ping et timeouts
- Configuration réseau (APN, domaines)
- Seuils de surveillance

## Points d'attention et bonnes pratiques

### Sécurité

**SSL en mode développement** : Le code utilise actuellement un TrustManager permissif pour les tests. En production, utiliser `createSecureSSLContext()`.

**Gestion des certificats** : Les certificats Enedis doivent être déployés dans l'image Linux finale.

### Performance et fiabilité

**Gestion des ressources** : Fermeture automatique des connexions et threads
**Retry automatique** : Mécanisme de retry pour les requêtes HTTP
**Logging structuré** : Niveaux de log configurables dynamiquement

### Développement

**Tests unitaires** : Couverture des cas critiques avec Mockito
**Architecture testable** : Injection de dépendances via constructeurs
**Séparation des responsabilités** : Ports & Adapters

## Configuration et déploiement

### Démarrage

**Ordre de lancement recommandé** :

```bash
# 1. Démarrer le HAL (Hardware Abstraction Layer)
mask run embedded-hal

# 2. Démarrer le mock SI (dans un autre terminal)
mask run si-mock

# 3. Initialiser la base de données (dans un autre terminal)
mask dev fill-test-data

# 4. Démarrer l'application principale
mask run main
```

**Commandes de développement** :

```bash
# Compilation rapide
mask install

# Tests avec couverture
mask coverage run

# Nettoyage
mask clean
```

### DevContainers

Le projet utilise DevContainers pour un environnement de développement standardisé.

**Prérequis** : WSL, Docker et identifiants Artifactory (`ARTIFACTORY_USER`, `ARTIFACTORY_TOKEN`)

**Configuration** :
1. Cloner le projet dans WSL
2. Copier `.devcontainer/secrets.env.sample` vers `.devcontainer/secrets.env` avec vos identifiants
3. Installer l'extension "Dev Containers" dans VSCode (cocher "Execute in WSL")
4. Cliquer sur `><` en bas à gauche → "Reopen in container"

## Modules détaillés

### ComSI (Communication SI)

**Responsabilité** : Interface avec le Système d'Information d'Enedis

**Composants clés** :
- `ComSI` : Service principal de communication
- `SiClientHttpAdapter` : Client HTTP pour les requêtes au SI
- `ConfigurationChangeWatcher` : Surveillance des changements de configuration
- `MetricsSender` : Envoi programmé des métriques

**Logique métier** :
- Envoi de configuration au démarrage si état INIT
- Surveillance continue des changements de paramètres
- Gestion de la redondance des datacenters
- Retry automatique avec fallback

### System (Gestion système)

**Responsabilité** : Surveillance et gestion du système embarqué

**Composants clés** :
- `ThreadWatchdog` : Surveillance des threads applicatifs
- `WatchdogService` : Interface avec systemd
- `SystemModule` : Gestion des services système
- `LedPort` : Contrôle des LEDs de signalisation

**Logique de surveillance** :
- Heartbeat périodique des threads critiques
- Détection automatique des deadlocks
- Redémarrage en cascade (thread → service → système)
- Signalisation visuelle des états

### Infrastructure

**Responsabilité** : Adaptateurs techniques et intégrations

**Adaptateurs réseau** :
- `JavalinHttpServer` : Serveur web REST avec SSL
- `SiClientHttpAdapter` : Client HTTP avec retry et compression
- `HttpClient` : Wrapper HTTP avec gestion SSL

**Adaptateurs persistance** :
- `SqliteParamsProvider` : Accès configuration SQLite
- `TelegrafJsonlMetricsSourceAdapter` : Lecture métriques Telegraf

**Adaptateurs système** :
- `DbusBoardManagerAdapter` : Communication carte mère
- `DbusModemBG95Adapter` : Communication modem
- `SystemdAdapter` : Gestion services systemd

---
